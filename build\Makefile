# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "F:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "F:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\ICAR_05\ICAR05_L_OT_ModelTest

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\ICAR_05\ICAR05_L_OT_ModelTest\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"F:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"F:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\ICAR_05\ICAR05_L_OT_ModelTest\build\CMakeFiles E:\ICAR_05\ICAR05_L_OT_ModelTest\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\ICAR_05\ICAR05_L_OT_ModelTest\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named ICAR05_L_OT_ModelTest

# Build rule for target.
ICAR05_L_OT_ModelTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 ICAR05_L_OT_ModelTest
.PHONY : ICAR05_L_OT_ModelTest

# fast build rule for target.
ICAR05_L_OT_ModelTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/build
.PHONY : ICAR05_L_OT_ModelTest/fast

src/PWNL.obj: src/PWNL.c.obj
.PHONY : src/PWNL.obj

# target to build an object file
src/PWNL.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL.c.obj
.PHONY : src/PWNL.c.obj

src/PWNL.i: src/PWNL.c.i
.PHONY : src/PWNL.i

# target to preprocess a source file
src/PWNL.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL.c.i
.PHONY : src/PWNL.c.i

src/PWNL.s: src/PWNL.c.s
.PHONY : src/PWNL.s

# target to generate assembly for a file
src/PWNL.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL.c.s
.PHONY : src/PWNL.c.s

src/PWNL_data.obj: src/PWNL_data.c.obj
.PHONY : src/PWNL_data.obj

# target to build an object file
src/PWNL_data.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL_data.c.obj
.PHONY : src/PWNL_data.c.obj

src/PWNL_data.i: src/PWNL_data.c.i
.PHONY : src/PWNL_data.i

# target to preprocess a source file
src/PWNL_data.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL_data.c.i
.PHONY : src/PWNL_data.c.i

src/PWNL_data.s: src/PWNL_data.c.s
.PHONY : src/PWNL_data.s

# target to generate assembly for a file
src/PWNL_data.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/PWNL_data.c.s
.PHONY : src/PWNL_data.c.s

src/const_params.obj: src/const_params.c.obj
.PHONY : src/const_params.obj

# target to build an object file
src/const_params.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/const_params.c.obj
.PHONY : src/const_params.c.obj

src/const_params.i: src/const_params.c.i
.PHONY : src/const_params.i

# target to preprocess a source file
src/const_params.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/const_params.c.i
.PHONY : src/const_params.c.i

src/const_params.s: src/const_params.c.s
.PHONY : src/const_params.s

# target to generate assembly for a file
src/const_params.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/const_params.c.s
.PHONY : src/const_params.c.s

src/look2_iu8lu32n24_binlcse.obj: src/look2_iu8lu32n24_binlcse.c.obj
.PHONY : src/look2_iu8lu32n24_binlcse.obj

# target to build an object file
src/look2_iu8lu32n24_binlcse.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/look2_iu8lu32n24_binlcse.c.obj
.PHONY : src/look2_iu8lu32n24_binlcse.c.obj

src/look2_iu8lu32n24_binlcse.i: src/look2_iu8lu32n24_binlcse.c.i
.PHONY : src/look2_iu8lu32n24_binlcse.i

# target to preprocess a source file
src/look2_iu8lu32n24_binlcse.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/look2_iu8lu32n24_binlcse.c.i
.PHONY : src/look2_iu8lu32n24_binlcse.c.i

src/look2_iu8lu32n24_binlcse.s: src/look2_iu8lu32n24_binlcse.c.s
.PHONY : src/look2_iu8lu32n24_binlcse.s

# target to generate assembly for a file
src/look2_iu8lu32n24_binlcse.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/src/look2_iu8lu32n24_binlcse.c.s
.PHONY : src/look2_iu8lu32n24_binlcse.c.s

testcase/RTE.obj: testcase/RTE.c.obj
.PHONY : testcase/RTE.obj

# target to build an object file
testcase/RTE.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/RTE.c.obj
.PHONY : testcase/RTE.c.obj

testcase/RTE.i: testcase/RTE.c.i
.PHONY : testcase/RTE.i

# target to preprocess a source file
testcase/RTE.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/RTE.c.i
.PHONY : testcase/RTE.c.i

testcase/RTE.s: testcase/RTE.c.s
.PHONY : testcase/RTE.s

# target to generate assembly for a file
testcase/RTE.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/RTE.c.s
.PHONY : testcase/RTE.c.s

testcase/TESTCASE_Demo.obj: testcase/TESTCASE_Demo.cpp.obj
.PHONY : testcase/TESTCASE_Demo.obj

# target to build an object file
testcase/TESTCASE_Demo.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_Demo.cpp.obj
.PHONY : testcase/TESTCASE_Demo.cpp.obj

testcase/TESTCASE_Demo.i: testcase/TESTCASE_Demo.cpp.i
.PHONY : testcase/TESTCASE_Demo.i

# target to preprocess a source file
testcase/TESTCASE_Demo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_Demo.cpp.i
.PHONY : testcase/TESTCASE_Demo.cpp.i

testcase/TESTCASE_Demo.s: testcase/TESTCASE_Demo.cpp.s
.PHONY : testcase/TESTCASE_Demo.s

# target to generate assembly for a file
testcase/TESTCASE_Demo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_Demo.cpp.s
.PHONY : testcase/TESTCASE_Demo.cpp.s

testcase/TESTCASE_PWN.obj: testcase/TESTCASE_PWN.cpp.obj
.PHONY : testcase/TESTCASE_PWN.obj

# target to build an object file
testcase/TESTCASE_PWN.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN.cpp.obj
.PHONY : testcase/TESTCASE_PWN.cpp.obj

testcase/TESTCASE_PWN.i: testcase/TESTCASE_PWN.cpp.i
.PHONY : testcase/TESTCASE_PWN.i

# target to preprocess a source file
testcase/TESTCASE_PWN.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN.cpp.i
.PHONY : testcase/TESTCASE_PWN.cpp.i

testcase/TESTCASE_PWN.s: testcase/TESTCASE_PWN.cpp.s
.PHONY : testcase/TESTCASE_PWN.s

# target to generate assembly for a file
testcase/TESTCASE_PWN.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN.cpp.s
.PHONY : testcase/TESTCASE_PWN.cpp.s

testcase/TESTCASE_PWN2.obj: testcase/TESTCASE_PWN2.cpp.obj
.PHONY : testcase/TESTCASE_PWN2.obj

# target to build an object file
testcase/TESTCASE_PWN2.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN2.cpp.obj
.PHONY : testcase/TESTCASE_PWN2.cpp.obj

testcase/TESTCASE_PWN2.i: testcase/TESTCASE_PWN2.cpp.i
.PHONY : testcase/TESTCASE_PWN2.i

# target to preprocess a source file
testcase/TESTCASE_PWN2.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN2.cpp.i
.PHONY : testcase/TESTCASE_PWN2.cpp.i

testcase/TESTCASE_PWN2.s: testcase/TESTCASE_PWN2.cpp.s
.PHONY : testcase/TESTCASE_PWN2.s

# target to generate assembly for a file
testcase/TESTCASE_PWN2.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN2.cpp.s
.PHONY : testcase/TESTCASE_PWN2.cpp.s

testcase/TESTCASE_PWN3.obj: testcase/TESTCASE_PWN3.cpp.obj
.PHONY : testcase/TESTCASE_PWN3.obj

# target to build an object file
testcase/TESTCASE_PWN3.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN3.cpp.obj
.PHONY : testcase/TESTCASE_PWN3.cpp.obj

testcase/TESTCASE_PWN3.i: testcase/TESTCASE_PWN3.cpp.i
.PHONY : testcase/TESTCASE_PWN3.i

# target to preprocess a source file
testcase/TESTCASE_PWN3.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN3.cpp.i
.PHONY : testcase/TESTCASE_PWN3.cpp.i

testcase/TESTCASE_PWN3.s: testcase/TESTCASE_PWN3.cpp.s
.PHONY : testcase/TESTCASE_PWN3.s

# target to generate assembly for a file
testcase/TESTCASE_PWN3.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN3.cpp.s
.PHONY : testcase/TESTCASE_PWN3.cpp.s

testcase/TESTCASE_PWN4.obj: testcase/TESTCASE_PWN4.cpp.obj
.PHONY : testcase/TESTCASE_PWN4.obj

# target to build an object file
testcase/TESTCASE_PWN4.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN4.cpp.obj
.PHONY : testcase/TESTCASE_PWN4.cpp.obj

testcase/TESTCASE_PWN4.i: testcase/TESTCASE_PWN4.cpp.i
.PHONY : testcase/TESTCASE_PWN4.i

# target to preprocess a source file
testcase/TESTCASE_PWN4.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN4.cpp.i
.PHONY : testcase/TESTCASE_PWN4.cpp.i

testcase/TESTCASE_PWN4.s: testcase/TESTCASE_PWN4.cpp.s
.PHONY : testcase/TESTCASE_PWN4.s

# target to generate assembly for a file
testcase/TESTCASE_PWN4.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TESTCASE_PWN4.cpp.s
.PHONY : testcase/TESTCASE_PWN4.cpp.s

testcase/TEST_API.obj: testcase/TEST_API.cpp.obj
.PHONY : testcase/TEST_API.obj

# target to build an object file
testcase/TEST_API.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TEST_API.cpp.obj
.PHONY : testcase/TEST_API.cpp.obj

testcase/TEST_API.i: testcase/TEST_API.cpp.i
.PHONY : testcase/TEST_API.i

# target to preprocess a source file
testcase/TEST_API.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TEST_API.cpp.i
.PHONY : testcase/TEST_API.cpp.i

testcase/TEST_API.s: testcase/TEST_API.cpp.s
.PHONY : testcase/TEST_API.s

# target to generate assembly for a file
testcase/TEST_API.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/TEST_API.cpp.s
.PHONY : testcase/TEST_API.cpp.s

testcase/gtest_main.obj: testcase/gtest_main.cc.obj
.PHONY : testcase/gtest_main.obj

# target to build an object file
testcase/gtest_main.cc.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/gtest_main.cc.obj
.PHONY : testcase/gtest_main.cc.obj

testcase/gtest_main.i: testcase/gtest_main.cc.i
.PHONY : testcase/gtest_main.i

# target to preprocess a source file
testcase/gtest_main.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/gtest_main.cc.i
.PHONY : testcase/gtest_main.cc.i

testcase/gtest_main.s: testcase/gtest_main.cc.s
.PHONY : testcase/gtest_main.s

# target to generate assembly for a file
testcase/gtest_main.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\ICAR05_L_OT_ModelTest.dir\build.make CMakeFiles/ICAR05_L_OT_ModelTest.dir/testcase/gtest_main.cc.s
.PHONY : testcase/gtest_main.cc.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... ICAR05_L_OT_ModelTest
	@echo ... src/PWNL.obj
	@echo ... src/PWNL.i
	@echo ... src/PWNL.s
	@echo ... src/PWNL_data.obj
	@echo ... src/PWNL_data.i
	@echo ... src/PWNL_data.s
	@echo ... src/const_params.obj
	@echo ... src/const_params.i
	@echo ... src/const_params.s
	@echo ... src/look2_iu8lu32n24_binlcse.obj
	@echo ... src/look2_iu8lu32n24_binlcse.i
	@echo ... src/look2_iu8lu32n24_binlcse.s
	@echo ... testcase/RTE.obj
	@echo ... testcase/RTE.i
	@echo ... testcase/RTE.s
	@echo ... testcase/TESTCASE_Demo.obj
	@echo ... testcase/TESTCASE_Demo.i
	@echo ... testcase/TESTCASE_Demo.s
	@echo ... testcase/TESTCASE_PWN.obj
	@echo ... testcase/TESTCASE_PWN.i
	@echo ... testcase/TESTCASE_PWN.s
	@echo ... testcase/TESTCASE_PWN2.obj
	@echo ... testcase/TESTCASE_PWN2.i
	@echo ... testcase/TESTCASE_PWN2.s
	@echo ... testcase/TESTCASE_PWN3.obj
	@echo ... testcase/TESTCASE_PWN3.i
	@echo ... testcase/TESTCASE_PWN3.s
	@echo ... testcase/TESTCASE_PWN4.obj
	@echo ... testcase/TESTCASE_PWN4.i
	@echo ... testcase/TESTCASE_PWN4.s
	@echo ... testcase/TEST_API.obj
	@echo ... testcase/TEST_API.i
	@echo ... testcase/TEST_API.s
	@echo ... testcase/gtest_main.obj
	@echo ... testcase/gtest_main.i
	@echo ... testcase/gtest_main.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

