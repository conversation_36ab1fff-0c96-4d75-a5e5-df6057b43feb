<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakeRunConfigurationManager" shouldGenerate="true" shouldDeleteObsolete="true">
    <generated>
      <config projectName="ICAR05_L_OT_ModelTest" targetName="ICAR05_L_OT_ModelTest" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" CONFIG_NAME="Debug" ENABLED="true" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bb678c15-35ad-4718-b7c3-2636a7ebbbe9" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/CMakeLists.txt" beforeDir="false" afterPath="$PROJECT_DIR$/CMakeLists.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_CMS.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_DLK_Aloof.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_DLK_Near.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_Demo.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_Demo.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_PWN.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_PWN.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_PWN.h" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_PWN.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_PWN2.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_PWN2.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_PWN3.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_PWN3.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TESTCASE_PWN4.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TESTCASE_PWN4.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TEST_API.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TEST_API.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/testcase/TEST_API.h" beforeDir="false" afterPath="$PROJECT_DIR$/testcase/TEST_API.h" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="ProjectId" id="2tW3evGwYnPkkvYSyGtIUgRaTpT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="cf.first.check.clang-format" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/testcase" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.path.for.package.tslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
    <property name="restartRequiresConfirmation" value="false" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\ICAR_05\ICAR05_L_OT_ModelTest\testcase" />
    </key>
  </component>
  <component name="RunManager" selected="Google Test.All in TESTCASE_PWN.cpp">
    <configuration default="true" type="CLionExternalRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="CLION.EXTERNAL.BUILD" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in TESTCASE_PWN3.cpp" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" temporary="true" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_PATTERN="all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN3.cpp)" TEST_MODE="PATTERN">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in TESTCASE_PWN.cpp and 3 more" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" temporary="true" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_PATTERN="all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN.cpp):all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN2.cpp):all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN3.cpp):all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN4.cpp)" TEST_MODE="PATTERN">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in TESTCASE_PWN.cpp" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" temporary="true" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_PATTERN="all-tests(file://$PROJECT_DIR$/testcase/TESTCASE_PWN.cpp)" TEST_MODE="PATTERN">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ICAR05_L_OT_ModelTest" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_MODE="SUITE_TEST">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="PWN.SI_TC_PWN_001" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" temporary="true" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_CLASS="PWN" TEST_METHOD="SI_TC_PWN_001" TEST_MODE="SUITE_TEST">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="PWN.SI_TC_PWN_1092" type="CMakeGoogleTestRunConfigurationType" factoryName="Google Test" temporary="true" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="ICAR05_L_OT_ModelTest" TARGET_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_PROJECT_NAME="ICAR05_L_OT_ModelTest" RUN_TARGET_NAME="ICAR05_L_OT_ModelTest" TEST_CLASS="PWN" TEST_METHOD="SI_TC_PWN_1092" TEST_MODE="SUITE_TEST">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Google Test.ICAR05_L_OT_ModelTest" />
      <item itemvalue="Google Test.All in TESTCASE_PWN3.cpp" />
      <item itemvalue="Google Test.All in TESTCASE_PWN.cpp and 3 more" />
      <item itemvalue="Google Test.PWN.SI_TC_PWN_001" />
      <item itemvalue="Google Test.PWN.SI_TC_PWN_1092" />
      <item itemvalue="Google Test.All in TESTCASE_PWN.cpp" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Google Test.All in TESTCASE_PWN.cpp" />
        <item itemvalue="Google Test.All in TESTCASE_PWN.cpp and 3 more" />
        <item itemvalue="Google Test.PWN.SI_TC_PWN_1092" />
        <item itemvalue="Google Test.PWN.SI_TC_PWN_001" />
        <item itemvalue="Google Test.All in TESTCASE_PWN3.cpp" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="E:\ICAR_05\ICAR05_L_OT_ModelTest" />
          <option name="myCopyRoot" value="E:\ICAR_05\ICAR05_L_OT_ModelTest" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="E:\ICAR_05\ICAR05_L_OT_ModelTest" />
          <option name="myCopyRoot" value="E:\ICAR_05\ICAR05_L_OT_ModelTest" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bb678c15-35ad-4718-b7c3-2636a7ebbbe9" name="Default Changelist" comment="" />
      <created>1740454101740</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740454101740</updated>
      <workItem from="1740454103011" duration="90000" />
      <workItem from="1740454262647" duration="1993000" />
      <workItem from="1740463382213" duration="1691000" />
      <workItem from="1740531989404" duration="2010000" />
      <workItem from="1740735197988" duration="1599000" />
      <workItem from="1741577233242" duration="21925000" />
      <workItem from="1741846577172" duration="1451000" />
      <workItem from="1742211609634" duration="93000" />
      <workItem from="1743989427035" duration="369000" />
      <workItem from="1743990803293" duration="21000" />
      <workItem from="1743990840577" duration="5892000" />
      <workItem from="1744004423744" duration="112000" />
      <workItem from="1744099211451" duration="1735000" />
      <workItem from="1745309869135" duration="38000" />
      <workItem from="1746776088842" duration="6265000" />
      <workItem from="1747029419250" duration="10528000" />
      <workItem from="1747125003369" duration="1652000" />
      <workItem from="1747127000263" duration="1637000" />
      <workItem from="1747208312936" duration="426000" />
      <workItem from="1747819181033" duration="1280000" />
      <workItem from="1750042813233" duration="14671000" />
      <workItem from="1750237737858" duration="1885000" />
      <workItem from="1750295272907" duration="248000" />
      <workItem from="1750295660318" duration="19044000" />
      <workItem from="1750639623483" duration="20030000" />
      <workItem from="1750751096688" duration="4761000" />
      <workItem from="1750815042944" duration="10186000" />
      <workItem from="1750835772712" duration="109000" />
      <workItem from="1750915685059" duration="4449000" />
      <workItem from="1751613903606" duration="445000" />
      <workItem from="1751944654406" duration="254000" />
      <workItem from="1752112382144" duration="16471000" />
      <workItem from="1752194850928" duration="8891000" />
      <workItem from="1752469222266" duration="30000" />
      <workItem from="1753864963997" duration="38526000" />
      <workItem from="1754268905695" duration="38472000" />
      <workItem from="1754386866943" duration="1102000" />
      <workItem from="1754457959359" duration="507000" />
      <workItem from="1755415944542" duration="1292000" />
      <workItem from="1755486174261" duration="229000" />
      <workItem from="1755487123791" duration="1235000" />
      <workItem from="1755496851638" duration="2493000" />
      <workItem from="1755568811185" duration="979000" />
      <workItem from="1755580401391" duration="6967000" />
      <workItem from="1755650503166" duration="60000" />
      <workItem from="1756772638524" duration="2337000" />
      <workItem from="1756885705831" duration="8994000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.bat" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="E:\ICAR_05\ICAR05_L_OT_ModelTest\cmake-build-debug" NAME="All in TESTCASE_PWN.cpp and 3 more Coverage Results" MODIFIED="1756948635656" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="gcov/llvm" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>