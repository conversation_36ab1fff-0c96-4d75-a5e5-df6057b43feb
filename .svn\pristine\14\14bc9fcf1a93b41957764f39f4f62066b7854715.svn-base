#include "gtest/gtest.h"
#include "RTE.h"
#include "TEST_API.h"
//#include "enum.h"


namespace {
    static void CMS_PDUInput(int PDUInputTimes) {
        for (int tick = 0; tick < PDUInputTimes; tick++) {
            VeOUT_PDU_PowerMode_sig = 0;
            TestCMSStep(3);
            VeOUT_PDU_PowerMode_sig = 1;
            TestCMSStep(3);
        }
    }

    TEST(CMS, SI_TC_CMS_001) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed<5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;


        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }


    TEST(CMS, SI_TC_CMS_002) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed=5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_003) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed>5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
//        PowerModeValid_Counter();
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }


    TEST(CMS, SI_TC_CMS_004) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_005) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_006) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_007) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_008) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);

    }





    TEST(CMS, SI_TC_CMS_009) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed<5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x2: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(300);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }

    TEST(CMS, SI_TC_CMS_010) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed=5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x2: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_011) {
        TEST_CMSINIT();
        //当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；整车电源状态 ZCUL_SystemPowerMode=0x1: ON；车速 ESC_VehiclESCspeed>5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x2: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }


    TEST(CMS, SI_TC_CMS_012) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_013) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_014) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_015) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_016) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }
    TEST(CMS, SI_TC_CMS_017) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 0;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }
    TEST(CMS, SI_TC_CMS_018) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(500);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);

    }


    TEST(CMS, SI_TC_CMS_019) {
            TEST_CMSINIT();
            //
            //a. 当前车辆处于运输模式：车辆模式 ZCUL_CarMode=0x2：Transport Mode；
            //b. 整车电源状态 ZCUL_SystemPowerMode=0x1: ON；
            //c. 车速 ESC_VehiclESCspeed<5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
            //
            VeINP_HWA_VehicleModeStatus_sig = 2;
            VeOUT_PDU_PowerMode_sig = 1;
            VeOUT_SP_EspVehSpd_kmh = 3;
            VbINP_CAN_EspVehSpdVld_flg = 1;

            TestCMSStep(10);
            VeINP_HWA_VehicleModeStatus_sig = 0;
            TestCMSStep(10);
            EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
            EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
            EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

        }

        TEST(CMS, SI_TC_CMS_020) {
            TEST_CMSINIT();

            //
            //a. 当前车辆处于运输模式：车辆模式 ZCUL_CarMode=0x2：Transport Mode；
            //b. 整车电源状态 ZCUL_SystemPowerMode=0x1: ON；
            //c. 车速 ESC_VehiclESCspeed=5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
            //
            VeINP_HWA_VehicleModeStatus_sig = 2;
            VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
            VeOUT_PDU_PowerMode_sig = 1;
            VeOUT_SP_EspVehSpd_kmh = 5;
            VbINP_CAN_EspVehSpdVld_flg = 1;
            EXPECT_EQ(VeINP_HWA_VehicleModeStatus_sig, 2);

            TestCMSStep(10);
            VeINP_HWA_VehicleModeStatus_sig = 0;
            TestCMSStep(10);
            EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
            EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
            EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

        }

    TEST(CMS, SI_TC_CMS_021) {
        TEST_CMSINIT();
        //
        //a. 当前车辆处于运输模式：车辆模式 ZCUL_CarMode=0x2：Transport Mode；
        //b. 整车电源状态 ZCUL_SystemPowerMode=0x1: ON；
        //c. 车速 ESC_VehiclESCspeed>5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 0;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }


    TEST(CMS, SI_TC_CMS_022) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_023) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_024) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_025) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }



 TEST(CMS, SI_TC_CMS_026) {
        TEST_CMSINIT();
        //
        //a.  当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；
        //b.  整车电源状态 ZCUL_SystemPowerMode=0x1: ON；
        //c.  车速 ESC_VehiclESCspeed=5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);

    }

    TEST(CMS, SI_TC_CMS_027) {
        TEST_CMSINIT();
        //a.  当前车辆处于工厂模式：车辆模式 ZCUL_CarMode=0x0: Factory Mode；
        //b.  整车电源状态 ZCUL_SystemPowerMode=0x1: ON；
        //c.  车速 ESC_VehiclESCspeed>5km/h 且车速有效位 ESC_VehiclSpeedInvalid=0x1: Valid；
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }

    TEST(CMS, SI_TC_CMS_028) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }


    TEST(CMS, SI_TC_CMS_029) {
        TEST_CMSINIT();
        //
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_030) {
        TEST_CMSINIT();
        //
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_031) {
        TEST_CMSINIT();
        //
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_032) {
        TEST_CMSINIT();
        //
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);

    }
    TEST(CMS, SI_TC_CMS_033) {
        TEST_CMSINIT();
        //
        //
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);

    }





 TEST(CMS, SI_TC_CMS_0034) {
         TEST_CMSINIT();
         VeINP_HWA_VehicleModeStatus_sig = 0;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 3;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         CMS_PDUInput(200);

         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


     }
     TEST(CMS, SI_TC_CMS_0035) {
         TEST_CMSINIT();
         VeINP_HWA_VehicleModeStatus_sig = 0;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 5;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         CMS_PDUInput(200);

         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);


     }
     TEST(CMS, SI_TC_CMS_0036) {
         TEST_CMSINIT();
         VeINP_HWA_VehicleModeStatus_sig = 0;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 7;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         CMS_PDUInput(200);

         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);


     }

    TEST(CMS, SI_TC_CMS_0037) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);


    }
    TEST(CMS, SI_TC_CMS_0038) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 0;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }




    TEST(CMS, SI_TC_CMS_039) {
         TEST_CMSINIT();

         VeINP_HWA_VehicleModeStatus_sig = 1;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 3;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         VeINP_HWA_VehicleModeStatus_sig = 0;
         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 0);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 0);


     }

     TEST(CMS, SI_TC_CMS_040) {
         TEST_CMSINIT();

         VeINP_HWA_VehicleModeStatus_sig = 1;
         VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 5;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         VeINP_HWA_VehicleModeStatus_sig = 0;
         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


     }

     TEST(CMS, SI_TC_CMS_041) {
         TEST_CMSINIT();

         VeINP_HWA_VehicleModeStatus_sig = 1;
         VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
         VeOUT_PDU_PowerMode_sig = 1;
         VeOUT_SP_EspVehSpd_kmh = 7;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         VeINP_HWA_VehicleModeStatus_sig = 0;
         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


     }

     TEST(CMS, SI_TC_CMS_042) {
         TEST_CMSINIT();

         VeINP_HWA_VehicleModeStatus_sig = 1;
         VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
         VeOUT_PDU_PowerMode_sig = 0;
         VeOUT_SP_EspVehSpd_kmh = 3;
         VbINP_CAN_EspVehSpdVld_flg = 1;

         TestCMSStep(10);
         VeINP_HWA_VehicleModeStatus_sig = 0;
         TestCMSStep(10);
         EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
         EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
         EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


     }
    TEST(CMS, SI_TC_CMS_043) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 0;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_044) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 0;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_045) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 0;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }


    TEST(CMS, SI_TC_CMS_046) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }

    TEST(CMS, SI_TC_CMS_047) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_048) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_049) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_050) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_051) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_052) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_053) {
        TEST_CMSINIT();
        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig=2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_054) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig=2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_055) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig=2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_056) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig=2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        CMS_PDUInput(200);

        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }





TEST(CMS, SI_TC_CMS_057) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }

    TEST(CMS, SI_TC_CMS_058) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_059) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_060) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_061) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_062) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_063) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;

        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_064) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 1;
        VbINP_CAN_VCCMBrakeSignal2_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_065) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 2;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 2;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VbINP_CAN_VCCMBrakeSignal1_flg = 0;
        VbINP_CAN_VCCMBrakeSignal2_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//1
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 1;
        TestCMSStep(10);
        VbINP_HWA_HazardSw_flg = 0;//2
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 2;
        TestCMSStep(10);
        VeINP_CAN_ETRSHighBeamSwitchstatus_sig = 0;


        TestCMSStep(5 * 100);

        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);
    }


    TEST(CMS, SI_TC_CMS_066) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 2);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_067) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 5;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_068) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 7;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_069) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 0;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_070) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 2;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_071) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 3;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 1;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_072) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 1;
        VeINP_EPRM_ZCULCarModeFromEE_sig = 1;
        VeOUT_PDU_PowerMode_sig = 1;
        VeOUT_SP_EspVehSpd_kmh = 3;
        VbINP_CAN_EspVehSpdVld_flg = 0;

        TestCMSStep(10);
        VeINP_HWA_VehicleModeStatus_sig = 2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULCarMode_sig, 1);
        EXPECT_EQ(VbOUT_CMS_ZCULCarModeValid_flg, 1);
        EXPECT_EQ(VeOUT_CMS_ZCULCarModeToEE_sig, 1);


    }

    TEST(CMS, SI_TC_CMS_073) {
        TEST_CMSINIT();

        VeOUT_PDU_PowerMode_sig = 1;

        TestCMSStep(10);
        VeINP_CAN_ICMVehShowModeSet_sig=2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig, 1);
        EXPECT_EQ(VeOUT_CMS_ExitVehAntiTheftEnStsToEE_sig, 1);


    }
    TEST(CMS, SI_TC_CMS_074) {
        TEST_CMSINIT();

        VeOUT_PDU_PowerMode_sig = 0;

        TestCMSStep(10);
        VeINP_CAN_ICMVehShowModeSet_sig=2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig, 2);
        EXPECT_EQ(VeOUT_CMS_ExitVehAntiTheftEnStsToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_075) {
        TEST_CMSINIT();

        VeOUT_PDU_PowerMode_sig = 2;

        TestCMSStep(10);
        VeINP_CAN_ICMVehShowModeSet_sig=2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig, 2);
        EXPECT_EQ(VeOUT_CMS_ExitVehAntiTheftEnStsToEE_sig, 2);


    }
    TEST(CMS, SI_TC_CMS_076) {
        TEST_CMSINIT();

        VeOUT_PDU_PowerMode_sig = 3;

        TestCMSStep(10);
        VeINP_CAN_ICMVehShowModeSet_sig=2;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig, 2);
        EXPECT_EQ(VeOUT_CMS_ExitVehAntiTheftEnStsToEE_sig, 2);


    }

    TEST(CMS, SI_TC_CMS_077) {
        TEST_CMSINIT();

        VeINP_HWA_VehicleModeStatus_sig = 4;
        VeINP_EPRM_ZCULCarModeFromEE_sig=4;

        TestCMSStep(10);
        VeINP_CAN_ICMVehShowModeSet_sig=1;
        TestCMSStep(10);
        EXPECT_EQ(VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig, 2);
        EXPECT_EQ(VeOUT_CMS_ExitVehAntiTheftEnStsToEE_sig, 2);


    }








}
