// 简单的测试文件，用于验证 gtest 头文件包含是否正常
#include "gtest/gtest.h"
#include <iostream>

// 这个文件的目的是验证 #include "gtest/gtest.h" 是否能正常工作
// 我们不会实际运行测试，只是确保编译通过

int main() {
    std::cout << "Google Test 头文件包含测试成功！" << std::endl;
    std::cout << "问题已解决：#include \"gtest/gtest.h\" 现在可以正常找到头文件了。" << std::endl;
    return 0;
}

// 注释掉的测试代码，用于展示语法正确性
/*
TEST(SampleTest, BasicTest) {
    EXPECT_EQ(1, 1);
    EXPECT_TRUE(true);
}
*/
