/*
 * File: PWNL_private.h
 *
 * Code generated for Simulink model 'PWNL'.
 *
 * Model version                  : 1.3
 * AtechLib version               :
 * Model author                   : SE31ATK
 * Model date                     : Tue Aug 19 10:35:29 2025
 * Simulink Coder version         : 9.7 (R2022a) 13-Nov-2021
 * C/C++ source code generated on : Tue Aug 19 11:16:41 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Intel->x86-64 (Windows64)
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PWNL_private_h_
#define RTW_HEADER_PWNL_private_h_
#include "rtwtypes.h"
#include "Rte_Type.h"
#include "PWNL.h"

extern uint32 plook_u32u8_even0ckan(uint8 u, uint8 bp0, uint8 bpSpace, uint32
  maxIndex);
extern void PWNL_AntiStopSource1_Init(uint8 *rty_SO_e_AntiStopSource);
extern void PWNL_AntiStopSource1(UInt8 rtu_SI_e_TCPWindowControl, UInt8
  rtu_SI_e_VoicewindowControl, UInt8 rtu_SI_e_DKMwindowControl, Boolean
  rtu_SI_b_AntipinchEnable, uint8 *rty_SO_e_AntiStopSource,
  DW_AntiStopSource1_PWNL_T *localDW);
extern void PWNL_DisPassengerEE_Init(boolean *rty_SO_b_DateToEE);
extern void PWNL_DisPassengerEE(Boolean rtu_SI_b_Req, Boolean
  rtu_SI_b_DateFromEE, boolean *rty_SO_b_DateToEE, DW_DisPassengerEE_PWNL_T
  *localDW);
extern void PWNL_Chart_Init(uint8 *rty_SO_e_StopSource_sig);
extern void PWNL_Chart(UInt8 rtu_SI_e_StallSts, uint8 rtu_SI_e_StopSource_sig,
  uint8 *rty_SO_e_StopSource_sig, DW_Chart_PWNL_T *localDW);
extern void PWNL_Chart1_Init(uint8 *rty_SO_e_WinPriority, uint8 *rty_SO_e_WinSrc,
  uint8 *rty_SO_e_TargetPos, uint8 *rty_SO_e_CtrlCmd);
extern void PWNL_Chart1(uint8 rtu_SI_e_WinPerformSts, uint8 rtu_SI_u8_DrvEvent,
  uint8 rtu_SI_u8_PsgEvent, uint8 rtu_SI_e_CtrlCmd, uint8 rtu_SI_e_CmdPriority,
  uint8 rtu_SI_e_CmdSource, uint8 rtu_SI_e_TargetPos, Boolean
  rtu_SI_b_ICMOffVehPowerKeepSwSt, UInt8 rtu_SI_e_AntiThelfSts, uint8
  *rty_SO_e_WinPriority, uint8 *rty_SO_e_WinSrc, uint8 *rty_SO_e_TargetPos,
  uint8 *rty_SO_e_CtrlCmd, DW_Chart1_PWNL_T *localDW);
extern void PWNL_Chart5_Init(uint8 *rty_SO_e_DrvSwSts, uint8 *rty_SO_e_PsgSwSts);
extern void PWNL_Chart5(uint8 rtu_SI_e_DrvSwSts, uint8 rtu_SI_e_PsgSwSts,
  boolean rtu_b_winSwEnable, boolean rtu_b_InhibitState, uint8
  *rty_SO_e_DrvSwSts, uint8 *rty_SO_e_PsgSwSts);
extern void PWNL_Chart_a_Init(uint8 *rty_SwSts, boolean *rty_b_ButtStuck, uint8 *
  rty_SO_u8_CmdPriority);
extern void PWNL_Chart_a(UInt8 rtu_D_Sw_StsIN, boolean rtu_SI_b_AntipinchEnable,
  uint8 rtu_CurControlState, boolean rtu_b_QuicklyToggle, UInt8
  rtu_SI_e_LastStallSts, uint8 *rty_SwSts, boolean *rty_b_ButtStuck, uint8
  *rty_SO_u8_CmdPriority, DW_Chart_PWNL_e_T *localDW);
extern void PWNL_psgGetSwSts_Init(uint8 *rty_SwSts, boolean *rty_b_ButtStuck,
  uint8 *rty_SO_u8_CmdPriority);
extern void PWNL_psgGetSwSts(Boolean rtu_AD_IN, Boolean rtu_AU_IN, Boolean
  rtu_MD_IN, Boolean rtu_MU_IN, boolean rtu_SI_b_AntipinchEnable, uint8
  rtu_CurControlState, boolean rtu_b_QuicklyToggle, UInt8 rtu_SI_e_LastStallSts,
  uint8 *rty_SwSts, boolean *rty_b_ButtStuck, uint8 *rty_SO_u8_CmdPriority,
  DW_psgGetSwSts_PWNL_T *localDW);
extern void PWNL_Posxiuzhen_Init(uint8 *rty_SO_e_WinPercentage);
extern void PWNL_Posxiuzhen(uint8 rtu_SI_e_WinPercentage, uint8
  *rty_SO_e_WinPercentage);
extern void PWNL_SendToExecutionUnit_Init(uint8 *rty_SO_u8_WinTrgPos);
extern void PWNL_SendToExecutionUnit(uint8 rtu_SI_u8_MulWinTrgPos, uint8
  rtu_SI_u8_G_MulWinCtrl, uint8 *rty_SO_u8_WinTrgPos,
  DW_SendToExecutionUnit_PWNL_T *localDW);
extern void PWNL_Chart_d_Init(uint8 *rty_SO_e_AlmWinTrgPos);
extern void PWNL_Chart_m(uint8 rtu_SI_e_AlmSource, uint8
  rtu_SI_e_ArmedCloseWndStsFromEE, UInt8 rtu_SI_e_apFeedBackRunSts, uint8
  rtu_SI_e_WinPercentage, uint8 *rty_SO_e_AlmWinTrgPos, DW_Chart_PWNL_e1_T
  *localDW);
extern void PWNL_Multimedia_Init(uint8 *rty_SO_u8_SwSts);
extern void PWNL_Multimedia(uint8 rtu_SI_u8_WinTrgtPos, UInt16
  rtu_SI_u16_WinPosMax, UInt16 rtu_SI_u16_WinPos, UInt8 rtu_SI_u8_WinFeedBack,
  uint8 *rty_SO_u8_SwSts, DW_Multimedia_PWNL_T *localDW);
extern void PWNL_SourceCMD_FR_Init(uint8 *rty_SO_e_CmdSourceCMD);
extern void PWNL_SourceCMD_FR(uint8 rtu_SI_e_CmdPriority, uint8
  rtu_SI_e_CmdSource, uint8 *rty_SO_e_CmdSourceCMD);
extern void PWNL_TboxStsToEv_Init(uint8 *rty_TboxSwEvent, uint8
  *rty_TboxSwEvent_new);
extern void PWNL_TboxStsToEv(uint8 rtu_TboxSwSts, uint8 *rty_TboxSwEvent, uint8 *
  rty_TboxSwEvent_new, DW_TboxStsToEv_PWNL_T *localDW);
extern void PWNL_drvStsToEv_Init(uint8 *rty_drvSwEvent, uint8
  *rty_drvSwEvent_new);
extern void PWNL_drvStsToEv(uint8 rtu_drvSwSts, uint8 *rty_drvSwEvent, uint8
  *rty_drvSwEvent_new, DW_drvStsToEv_PWNL_T *localDW);
extern void PWNL_drvSwManualEn_Init(boolean *rty_ManuKeyNullStateFlag);
extern void PWNL_drvSwManualEn(uint8 rtu_drvSwSts, boolean
  *rty_ManuKeyNullStateFlag, DW_drvSwManualEn_PWNL_T *localDW);
extern void PWNL_psgSwManualEn_Init(boolean *rty_ManuKeyNullStateFlag);
extern void PWNL_psgSwManualEn(uint8 rtu_psgSwSts, boolean
  *rty_ManuKeyNullStateFlag, DW_psgSwManualEn_PWNL_T *localDW);
extern void PWNL_DrvInputInhibit_Init(uint8 *rty_ValidRequest);
extern void PWNL_DrvInputInhibit(uint8 rtu_Request, boolean rtu_b_vbatNormalFlag,
  boolean rtu_b_ohpMode, uint8 rtu_u8_stallDnConut, uint8 rtu_u8_stallUpConut,
  boolean rtu_cranking, uint8 *rty_ValidRequest);
extern void PWNL_PsgInputInhibit_Init(uint8 *rty_ValidRequest);
extern void PWNL_PsgInputInhibit(uint8 rtu_Request, boolean rtu_b_winSwEnable,
  boolean rtu_b_vbatNormalFlag, boolean rtu_b_InhibitState, boolean
  rtu_b_ohpMode, uint8 rtu_u8_stallDnConut, uint8 rtu_u8_stallUpConut, boolean
  rtu_cranking, uint8 *rty_ValidRequest);
extern void PWNL_RfInputInhibit_Init(uint8 *rty_ValidRequest);
extern void PWNL_RfInputInhibit(uint8 rtu_Request, boolean rtu_b_ign_in, boolean
  rtu_b_vbatNormalFlag, boolean rtu_b_ohpMode, uint8 rtu_u8_stallDnConut, uint8
  rtu_u8_stallUpConut, boolean rtu_cranking, boolean rtu_b_OTAON_In, uint8
  *rty_ValidRequest);
extern void PWNL_TboxInputInhibit_Init(uint8 *rty_SO_TboxEvent, uint8
  *rty_SO_VoiceEvent, uint8 *rty_u8_FiltterMinorFault);
extern void PWNL_TboxInputInhibit(uint8 rtu_TboxSwEvent, uint8 rtu_VoiceSwEvent,
  boolean rtu_b_VoiceCmdEnable_In, boolean rtu_b_vbatNormalFlag, boolean
  rtu_b_ohpMode, boolean rtu_cranking, boolean rtu_b_AntipinchEnable_IN, uint8
  *rty_SO_TboxEvent, uint8 *rty_SO_VoiceEvent, uint8 *rty_u8_FiltterMinorFault,
  DW_TboxInputInhibit_PWNL_T *localDW);
extern void PWNL_inputMerge_Init(uint8 *rty_Request, uint8 *rty_Request_Record);
extern void PWNL_inputMerge(uint8 rtu_drvSwEvent, uint8 rtu_psgSwEvent, uint8
  rtu_RfEvent, uint8 rtu_TboxEvent, uint8 rtu_VoiceEvent, uint8 *rty_Request,
  uint8 *rty_Request_Record);
extern void PWNL_window(uint8 rtu_newControlState, boolean rtu_b_Ign_In, boolean
  rtu_b_inhibitState, boolean rtu_b_EnableState, boolean rtu_b_crankIn, uint8
  rtu_u8_stallThresh, UInt8 rtu_u8_apCallBackRunSts, boolean
  rtu_b_doorFrontAjar_In, boolean rtu_b_vbatNormalFlag, uint8
  rtu_SI_u8_speEnviNum, uint8 rtu_SI_u8_ActStsNum, uint8 rtu_u8_currentValue,
  boolean rtu_SI_manuKeyNullStateFlg, boolean rtu_b_CarWash, boolean
  rtu_CarWashSts, uint8 rtu_u8_FiltterMinorFault, boolean rtu_b_ACC_In, boolean
  rtu_SI_b_TrkAntiUnlckEvt, boolean rtu_b_armingSts, boolean rtu_b_OTAON_In,
  boolean rtu_b_doorFourAjar_In, boolean rtu_SI_b_DoorOpenWinEN, uint8
  rtu_SI_e_WinPriority, uint8 rtu_SI_e_WinSrc, B_window_PWNL_T *localB,
  DW_window_PWNL_T *localDW);
extern void PWNL_Remapping_Init(uint8 *rty_SO_e_WINCtrlCmd);
extern void PWNL_Remapping(uint8 rtu_SI_e_WINCtrlCmd, uint8 *rty_SO_e_WINCtrlCmd,
  DW_Remapping_PWNL_T *localDW);
extern void PWNL_Postion_Init(uint8 *rty_SO_e_WinPercentage);
extern void PWNL_Postion(UInt16 rtu_SI_u_WinPostion, UInt16 rtu_SI_u_WinPosMax,
  uint8 *rty_SO_e_WinPercentage);
extern void PWNL_StopSource1_Init(uint8 *rty_SO_e_StopSorce);
extern void PWNL_StopSource1(uint8 rtu_SI_e_ModeStopSorce, UInt8
  rtu_SI_e_HWAStopSorce, uint8 *rty_SO_e_StopSorce);
extern void PWNL_StopSourceAdd1_Init(uint8 *rty_SO_e_StopSource);
extern void PWNL_StopSourceAdd1(uint8 rtu_SI_e_StopSorce, uint8
  rtu_SI_e_AntiStopSource, uint8 *rty_SO_e_StopSource);

#endif                                 /* RTW_HEADER_PWNL_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
