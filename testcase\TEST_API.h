//
// Created by allen on 2021/3/17.
//
#include "rtwtypes.h"
#ifndef CX62BTEST_TEST_API_H
#define CX62BTEST_TEST_API_H

#include "TESTCASE_Itf.h"

extern UInt8 Cyclecount;
extern UInt8 countsum;

void TestStep(unsigned int cycles);


//int Trigger_thermal_protection(UInt8 &State_of_motion,int Decided);



void TEST_INIT();
void TEST_ALMINIT();
void TEST_CMSINIT();
void TEST_DLKINIT();
void TEST_HORNINIT();
void TEST_INLINIT();
void TEST_IRCINIT();
void TEST_LITINIT();
void TEST_MIRINIT();
void TEST_PDUINIT();
void TEST_PWNINIT();
void TEST_SAFLITINIT();
void TEST_SAFPWNINIT();
void TEST_SAFSDUINIT();
void TEST_SAFTURNINIT();
void TEST_SDHUINIT();
void TEST_SDUINIT();
void TEST_SPINIT();
void TEST_TPMSINIT();
void TEST_TURNINIT();
void TEST_WWINIT();
void TEST_SAFWWINIT();


void TestALMStep(unsigned int  cycles);

void TestCMSStep(unsigned int  cycles);
void TestDLKStep(unsigned int  cycles);
void TestDMSStep(unsigned int  cycles);
void TestFTLStep(unsigned int  cycles);
void TestHORNStep(unsigned int  cycles);
void TestINLStep(unsigned int  cycles);
void TestIRCStep(unsigned int  cycles);
void TestLITStep(unsigned int  cycles);
void TestMIRStep(unsigned int  cycles);
void TestPDUStep(unsigned int  cycles);
void TestPWNStep(unsigned int  cycles);
void TestSAFLITStep(unsigned int  cycles);
void TestSAFPWNStep(unsigned int  cycles);
void TestSAFSDUStep(unsigned int  cycles);
void TestSAFTURNStep(unsigned int  cycles);
void TestSAFWWStep(unsigned int  cycles);
void TestSDHUStep(unsigned int  cycles);
void TestSDUStep(unsigned int  cycles);
void TestSPStep(unsigned int  cycles);
void TestTPMSStep(unsigned int  cycles);
void TestTURNStep(unsigned int  cycles);
void TestWWStep(unsigned int  cycles);


unsigned int Test_CountCycles(bool target,bool reset);

/****************************************************************************/
/**
 * Function Name: Test_CycleTestStep
 * Description: none
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

void Test_CycleTestStep();

/****************************************************************************/
/**
 * Function Name: Test_CycleTestRegister
 * Description: ע������ı��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/
 
void Test_CycleTestRegister(boolean_T* testTarget, uint8_T testIdx, boolean_T RW_FLAG);

/****************************************************************************/
/**
 * Function Name: Test_CycleTestInit
 * Description: ��ʼ������ע�����
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/
 
void Test_CycleTestInit();

/****************************************************************************/
/**
 * Function Name: Test_GetCycles
 * Description: ��ȡ��Ӧ��ű�����0->1�������
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/
 
uint16_T Test_GetCycles(uint8_T testIdx);

/****************************************************************************/
/**
 * Function Name: Test_GetOnDutyMax
 * Description: ��ȡ��Ӧ��ű�����0->1����󱣳ֵ����ʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOnDutyMax(uint8_T testIdx);

/****************************************************************************/
/**
 * Function Name: Test_GetOnDutyMin
 * Description: ��ȡ��Ӧ��ű�����0->1����󱣳ֵ���Сʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOnDutyMin(uint8_T testIdx);

/****************************************************************************/
/**
 * Function Name: Test_GetOffDutyMax
 * Description: ��ȡ��Ӧ��ű�����1->0����󱣳ֵ����ʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOffDutyMax(uint8_T testIdx);

/****************************************************************************/
/**
 * Function Name: Test_GetOffDutyMin
 * Description: ��ȡ��Ӧ��ŵı�����1->0����󱣳ֵ���Сʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOffDutyMin(uint8_T testIdx);

void TestDebugEnable(void);

void TestDebugDisable(void);

void TestBLTDebugEnable(void);

void TestBLTDebugDisable(void);

void TestCount(int);

/**
 * 解防输入信号
 */
void TestALARM_DISARM1Status(void);
/**
 * 预设防输入信号
 */
//void TestALARM_PREARM1Status(void);
/**
 * 设防_RKE输入信号
 */
void TestALARM_ARMING1Status_RKE(void);
/**
 * 设防_PE输入信号
 */
void TestALARM_ARMING1Status_PE(void);
/**
 * 设防_机械钥匙输入信号
 */
void TestALARM_ARMING1Status_MEC(void);
/**
 * 设防_TBOX闭锁输入信号
 */
void TestALARM_ARMING1Status_TBOX(void);
/**
 * 设防_PEPS离开闭锁输入信号
 */
void TestALARM_ARMING1Status_PEAWAY(void);
/**
 * 位置灯输出有效
 */
void TestParkLightSucc(void);

/**
 * 位置灯关闭_电源模式切换为OFF
 */
void TestParkLightClose0(void);
/**
 * 位置灯关闭_位置灯开关无效且近光灯开关无效;
 */
void TestParkLightClose1(void);

/**
 * 整个设防动作，以RKE设防为例
 */
void TestDISARM1_To_ARMING1(void);
void TestRemoteOnStatus(void);
/**
 * 触发pe解锁
 */
void TestPEUNLOCK(void);
/**
 * 触发pe闭锁
 */
void TestPELOCK(void);

void TestPETrunkUnLOCK(void);
//boolean Test4DoorAjar(boolean fourDoorAjar);
//boolean Test6DoorAjar(boolean sixDoorAjar);

#endif //CX62BTEST_TEST_API_H
