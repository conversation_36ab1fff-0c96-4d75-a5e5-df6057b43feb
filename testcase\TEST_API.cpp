//
// Created by allen on 2021/3/17.
//
#include <cstdio>
#include "TEST_API.h"
#include "gtest/gtest.h"
//#include <windows.h>
#include "RTE.h"
#include "PWNL.h"
#include "TESTCASE_PWN.h"

UInt8 Cyclecount = 0;
UInt8 countsum = 0;

static uint8_T s_u8_testIdx_max = 0x00;
static uint16_T s_u16_GetCycles[16]  = {0x00};
static uint16_T s_u16_GetON_MAX[16]  = {0x00};
static uint16_T s_u16_GetON_MIN[16]  = {0x00};
static uint16_T s_u16_GetOFF_MAX[16] = {0x00};
static uint16_T s_u16_GetOFF_MIN[16] = {0x00};

static uint16_T u16_Register_ON_time[16]  = {0x00};
static uint16_T u16_Register_OFF_time[16] = {0x00};

static uint8_T u8_Register_flag[16] = {0x00};

static boolean_T *regis[16] = {(boolean_T*)0x00};
static boolean_T b_Wirte_Id_flag = false;
static boolean_T b_signal_output[16] = {false};
static boolean_T b_old_signal_sts[16] = {false};
static boolean_T f_debugLog = false;
static boolean_T f_testBLTdebugLog = false;
boolean_T f_fourDoorAjar = false;
static boolean_T f_sixDoorAjar = false;

void TestDebug(void)
{
//    SYSTEMTIME sys;
//    GetLocalTime( &sys );
    static int idx = 0;
    static int t = 0;
    static bool old = 0;
    static bool oldRLOutMovTypeState= 0;
    static bool oldIGN=0;
    static bool oldFLOutMovTypeStateRelay =0;
    static bool oldFROutMovTypeStateRelay =0;
    static bool oldFobValid = 0;
    static uint8_T oldPDUState= 0;
    static uint8_T oldTurnMode = 0;
//    if (f_debugLog)
//    {
////        if (idx<=1)
////        {
////            idx++;
////        }
////        else
////        {
////            idx=0;
////            t++;
////        }
//        if (idx == 0) {
//            t++;
//            if(oldPDUState !=VbOUT_TURN_ZCULTurnLeftLightInd_flg)
//            {
//                printf("TimeStamp: %d ", t);
//                printf("VbOUT_TURN_ZCULTurnLeftLightInd_flg changed to : %d\n", VbOUT_TURN_ZCULTurnLeftLightInd_flg);
//                oldPDUState =VbOUT_TURN_ZCULTurnLeftLightInd_flg;
//            }
//            printf("VbOUT_TURN_ZCULTurnLeftLightInd_flg : %d\n", VbOUT_TURN_ZCULTurnLeftLightInd_flg);
////            printf( "%4d/%02d/%02d %02d:%02d:%02d.%03d \n",sys.wYear,sys.wMonth,sys.wDay,sys.wHour,sys.wMinute, sys.wSecond,sys.wMilliseconds);
//        }
//
//        if (idx == 0) {
//            t++;
//            if(oldPDUState !=VeOUT_TURN_ZCULTurnRightlightInd_sig)
//            {
//                printf("TimeStamp: %d ", t);
//                printf("VeOUT_TURN_ZCULTurnRightlightInd_sig changed to : %d\n", VeOUT_TURN_ZCULTurnRightlightInd_sig);
//                oldPDUState =VeOUT_TURN_ZCULTurnRightlightInd_sig;
//            }
//            printf("VeOUT_TURN_ZCULTurnRightlightInd_sig : %d\n", VeOUT_TURN_ZCULTurnRightlightInd_sig);
////            printf( "%4d/%02d/%02d %02d:%02d:%02d.%03d \n",sys.wYear,sys.wMonth,sys.wDay,sys.wHour,sys.wMinute, sys.wSecond,sys.wMilliseconds);
//        }
//    }

}

//static int Internal_counting=0;
//static int Mark_bit=0;
///**
// * @param Trigger_thermal_protection 触发热保护
// * @param State_of_motion 运动状态
// * @param Decided 是否打印当前计数：1：打印 0：不打印
// */
//int Trigger_thermal_protection(UInt8 &State_of_motion,int Decided)
//{
//    if(Decided==0)
//    {
//        if((State_of_motion==3||State_of_motion==1)&&Mark_bit==1)
//        {
//            Internal_counting=Internal_counting+3;
//        }
//        else if((State_of_motion==2||State_of_motion==4)&&Mark_bit==2)
//        {
//            Internal_counting=Internal_counting+2;
//        }
//        else if(State_of_motion==0&&Internal_counting>0&&Mark_bit==0)
//        {
//            Internal_counting=Internal_counting-1;
//        }
//
//
//        if((State_of_motion==3||State_of_motion==1)&&Mark_bit==0)
//        {
//            Mark_bit=1;
//        }
//        else if((State_of_motion==2||State_of_motion==4)&&Mark_bit==0)
//        {
//            Mark_bit=2;
//        }
//        else if(State_of_motion==0&&Internal_counting>0)
//        {
//            Mark_bit=0;
//        }
//    }
//
//    return Internal_counting;
//}

void TestPWNStep(unsigned int  cycles)
{
    int i=0;
    for(i=0;i<cycles;i++)
    {
//        setDebugTxt(i);
        PWN_Step();
        Test_CycleTestStep();
        TestDebug();
//        Trigger_thermal_protection(VmOUT_PWN_FLOutMovTypeState_enum,0);////限制较大后续仍需要修改;
    }
}

void TEST_PWNINIT()
{
    TEST_RESET();
    PWN_Init();
    VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;////乘客侧车窗禁止使能状态(EE读取);
    VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;////下雨关窗使能状态需要存储到EEPROM;
    VeOUT_ALM_ZCULAntiThelfSts_sig=1;////防盗状态;
}


void TEST_INIT(){
    TEST_RESET();
//    ALC_Init();
////    ALM_Init();
//    BSV_Init();
//    BT_Init();
//    CM_Init();
//    DLK_Init();
//    GW_Init();
//    HOR_Init();
//    IHU_Init();
////    IMMO_Init();
//    INL_Init();
//    LIT_Init();
//    ODO_Init();
//    PDU_Init();
//    PWN_Init();
//    RDEF_Init();
//    SP_Init();
//    TBOX_Init();
//    TPMS_Init();
//    TRK_Init();
//    TURN_Init();
////    SDU_Init();
////    SHU_Init();
////    VCU_Init();
//    WARN_Init();
//    WW_Init();
//    SRF_Init();
}

void TestDebugEnable(void)
{
    f_debugLog = true;
}

void TestDebugDisable(void)
{
    f_debugLog= false;
}
void TestBLTDebugEnable(void)
{
    f_testBLTdebugLog = true;
}

void TestBLTDebugDisable(void)
{
    f_testBLTdebugLog= false;
}


unsigned int Test_CountCycles(bool target,bool reset)
{
    static int cycles = 0;
    static bool f_startup = true;
    static bool oldTarget = false;
    if (reset == true)
    {
       cycles = 0;
    }
    else
    {
        if(oldTarget!=target
            && oldTarget == false)
        {
            cycles++;
        }
        oldTarget = target;
    }
    return cycles;

}

/****************************************************************************/
/**
 * Function Name: Test_CycleTestStep
 * Description: none
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22,  create this function
 ****************************************************************************/
void Test_CycleTestStep()
{

	
	if(false == b_Wirte_Id_flag)
	{
		return ;
	}
	
	for(int testIdx = 0; testIdx <= s_u8_testIdx_max; testIdx++)
	{
		Test_CycleTestRegister(&b_signal_output[testIdx],testIdx,false);
		if(b_old_signal_sts[testIdx] != b_signal_output[testIdx])
		{
			b_old_signal_sts[testIdx] = b_signal_output[testIdx];
			
			if(b_old_signal_sts[testIdx] == true)
			{
				s_u16_GetCycles[testIdx]++;
				u8_Register_flag[testIdx] = 0x01;
                s_u16_GetOFF_MAX[testIdx] = (s_u16_GetOFF_MAX[testIdx] > u16_Register_OFF_time[testIdx]) ? s_u16_GetOFF_MAX[testIdx] : u16_Register_OFF_time[testIdx];

                if( (s_u16_GetOFF_MIN[testIdx] == 0x00 )
                    || (s_u16_GetOFF_MIN[testIdx] > u16_Register_OFF_time[testIdx])
                        )
                {
                    s_u16_GetOFF_MIN[testIdx] = u16_Register_OFF_time[testIdx];
                }
                u16_Register_ON_time[testIdx] = 0;
			}
			else
			{
				u8_Register_flag[testIdx] = 0x02;
                s_u16_GetON_MAX[testIdx] = (s_u16_GetON_MAX[testIdx] > u16_Register_ON_time[testIdx]) ? s_u16_GetON_MAX[testIdx] : u16_Register_ON_time[testIdx];

                if( (s_u16_GetON_MIN[testIdx] == 0x00 )
                    || (s_u16_GetON_MIN[testIdx] > u16_Register_ON_time[testIdx])
                        )
                {
                    s_u16_GetON_MIN[testIdx] = u16_Register_ON_time[testIdx];
                }
                u16_Register_OFF_time[testIdx] = 0;
			}

		}
		if(u8_Register_flag[testIdx] == 0x01)
		{
			u16_Register_ON_time[testIdx]++;

		}
		else if(u8_Register_flag[testIdx] == 0x02)
		{			
			u16_Register_OFF_time[testIdx]++;

		}
	}
}
/****************************************************************************/
/**
 * Function Name: Test_CycleTestRegister
 * Description: ע������ı��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

void Test_CycleTestRegister(boolean_T* testTarget, uint8_T testIdx, boolean_T RW_FLAG)
{
	if(testIdx > 15 || testIdx < 0)
	{
		return ;
	}
	if(RW_FLAG)
	{
		b_Wirte_Id_flag = true;
		s_u8_testIdx_max = (s_u8_testIdx_max > testIdx) ? s_u8_testIdx_max : testIdx;
		regis[testIdx] = testTarget;
	}
	else
	{
		if(regis[testIdx]==(boolean_T*)0x00)
		{
			return ;
		}
		*testTarget = *regis[testIdx];
	}
	return;
}
/****************************************************************************/
/**
 * Function Name: Test_CycleTestInit
 * Description: ��ʼ������ע�����
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

void Test_CycleTestInit()
{
    boolean_T i;
	s_u8_testIdx_max = 0x00;
	
	memset(s_u16_GetCycles,0x00,16);
	memset(s_u16_GetON_MAX,0x00,16);
	memset(s_u16_GetON_MIN,0x00,16);	
	memset(s_u16_GetOFF_MAX,0x00,16);
	memset(s_u16_GetOFF_MIN,0x00,16);
	for (i = 0; i<16; i++)
    {
        b_signal_output[i] = false;
        b_old_signal_sts[i] = false;
    }
    memset(u16_Register_ON_time,0x00,16);
	memset(u16_Register_OFF_time,0x00,16);
	memset(regis,0x00,sizeof(regis));
	memset(u8_Register_flag,0x00,16);
	b_Wirte_Id_flag = false;
}

/****************************************************************************/
/**
 * Function Name: Test_GetCycles
 * Description: ��ȡ��Ӧ��ű�����0->1�������
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetCycles(uint8_T testIdx)
{
	return s_u16_GetCycles[testIdx];
}

/****************************************************************************/
/**
 * Function Name: Test_GetOnDutyMax
 * Description: ��ȡ��Ӧ��ű�����0->1����󱣳ֵ����ʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOnDutyMax(uint8_T testIdx)
{
	return s_u16_GetON_MAX[testIdx];
}

/****************************************************************************/
/**
 * Function Name: Test_GetOnDutyMin
 * Description: ��ȡ��Ӧ��ű�����0->1����󱣳ֵ���Сʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOnDutyMin(uint8_T testIdx)
{	
	return s_u16_GetON_MIN[testIdx];
}

/****************************************************************************/
/**
 * Function Name: Test_GetOffDutyMax
 * Description: ��ȡ��Ӧ��ű�����1->0����󱣳ֵ����ʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOffDutyMax(uint8_T testIdx)
{
	
	return s_u16_GetOFF_MAX[testIdx];
}

/****************************************************************************/
/**
 * Function Name: Test_GetOffDutyMin
 * Description: ��ȡ��Ӧ��ŵı�����1->0����󱣳ֵ���Сʱ��
 *
 * Param:   none
 * Return:  none
 * Author:  2021/03/22, xinhao.xia create this function
 ****************************************************************************/

uint16_T Test_GetOffDutyMin(uint8_T testIdx)
{
	return s_u16_GetOFF_MIN[testIdx];
	
}



//void TestCount(int index)
//{
//    static int idx = 0;
//    static int t = 0;
//    static bool old = 0;
//    static int SL_count = 0;
//
//    if (idx == 0) {
//
//        t++;
//        switch(index)
//        {
//
////                printf("TimeStamp: %d ", t);
////                printf("VeOUT_TUR_BDCS15LeftTurnLightReq_sig changed to : %d\n", VeOUT_TUR_BDCS15LeftTurnLightReq_sig);
//            case 1: {
//                if (VeOUT_TUR_BDCS15LeftTurnLightReq_sig != 0 && VeOUT_TUR_BDCS15LeftTurnLightReq_sig != 1) {
////                    printf("TimeStamp: %d ", t);
////                    printf("VeOUT_TUR_BDCS15LeftTurnLightReq_sig changed to : %d\n",VeOUT_TUR_BDCS15LeftTurnLightReq_sig);
//                    old = VeOUT_TUR_BDCS15LeftTurnLightReq_sig;
//
//                    SL_count++;
////                    printf("Cyclecount changed to : %d\n", SL_count);
//                    Cyclecount = SL_count;
////                    printf("Cyclecount changed to : %d\n", Cyclecount);
////            countsum = (int)Cyclecount/20;
//                    if ((int) Cyclecount / 20 == 1) {
//                        countsum++;
////                        printf("countsum changed to : %d\n", countsum);
//                    }
//
//                } else {
////            countsum = 0;
//                    SL_count = 0;
//                }break;
//            }
//
////                printf("TimeStamp: %d ", t);
////                printf("VeOUT_TUR_BDCS15LeftTurnLightReq_sig changed to : %d\n", VeOUT_TUR_BDCS15LeftTurnLightReq_sig);
//            case 2: {
//                if (VeOUT_TUR_BDCS15RightTurnLightReq_sig != 0 && VeOUT_TUR_BDCS15RightTurnLightReq_sig != 1) {
////            printf("TimeStamp: %d ", t);
////                    printf("VeOUT_TUR_BDCS15LeftTurnLightReq_sig changed to : %d\n",VeOUT_TUR_BDCS15RightTurnLightReq_sig);
//                    old = VeOUT_TUR_BDCS15RightTurnLightReq_sig;
//
//                    SL_count++;
//                    Cyclecount = SL_count;
////                    printf("Cyclecount changed to : %d\n", Cyclecount);
////            countsum = (int)Cyclecount/20;
//                    if ((int) Cyclecount / 20 == 1) {
//                        countsum++;
////                        printf("countsum changed to : %d\n", countsum);
//                    }
//
//                } else {
////            countsum = 0;
//                    SL_count = 0;
//                }break;
//            }
//            default:
//                break;
//        }
//
//    }
//}