<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakeWorkspace" PROJECT_DIR="$PROJECT_DIR$" />
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="https://192.168.8.127:8443/svn/VIU_Chery_ICAR05_L_TC377_Repository/Documents" />
                  <option value="https://192.168.8.127:8443/svn/VIU_Chery_ICAR05_L_TC377_Repository/branches" />
                  <option value="https://192.168.8.127:8443/svn/VIU_Chery_ICAR05_L_TC377_Repository/tags" />
                </list>
              </option>
              <option name="trunkUrl" value="https://192.168.8.127:8443/svn/VIU_Chery_ICAR05_L_TC377_Repository/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>