/* This file contains stub implementations of the AUTOSAR RTE functions.
   The stub implementations can be used for testing the generated code in
   Simulink, for example, in SIL/PIL simulations of the component under
   test. Note that this file should be replaced with an appropriate RTE
   file when deploying the generated code outside of Simulink.

   This file is generated for:
   Atomic software component:  "WW"
   ARXML schema: "4.3"
   File generated on: "23-Aug-2023 08:56:25"  */

#ifndef Rte_Type_h
#define Rte_Type_h
#include "rtwtypes.h"
#include "Std_Types.h"

/* AUTOSAR RTE Status Types */
#ifndef RTE_E_OK
#define RTE_E_OK                       (0x00)
#endif

#ifndef RTE_E_LOST_DATA
#define RTE_E_LOST_DATA                (0x40)
#endif

#ifndef RTE_E_LIMIT
#define RTE_E_LIMIT                    (0x82)
#endif

#ifndef E2E_E_OK
#define E2E_E_OK                       (0x00)
#endif

#ifndef E2EPW_STATUS_OK
#define E2EPW_STATUS_OK                (0x00)
#endif

#ifndef E2EPW_STATUS_OKSOMELOST
#define E2EPW_STATUS_OKSOMELOST        (0x20)
#endif

/* AUTOSAR Implementation data types, specific to software component */
typedef boolean Boolean;
typedef sint8 SInt8;
typedef uint8 UInt8;
typedef uint16 UInt16;
typedef sint16 SInt16;
typedef float32 Float;

/* AUTOSAR Array Types */
typedef UInt8 rt_Array_UInt8_8[8];
typedef UInt8 rt_Array_UInt8_10[10];
typedef UInt16 rt_Array_UInt16_16[16];
typedef UInt8 rt_Array_UInt8_6[6];
typedef UInt8 rt_Array_UInt8_9[9];
typedef void* Rte_Instance;


#ifndef RTW_HEADER_Rte_Type_h_
#define RTW_HEADER_Rte_Type_h_
#include "rtwtypes.h"
typedef uint8 RWIPMOD_E;

/* enum RWIPMOD_E */
#define RWIPMOD_E_OFF                  ((RWIPMOD_E)0U)           /* Default value */
#define RWIPMOD_E_Rest                 ((RWIPMOD_E)1U)
#define RWIPMOD_E_Low                  ((RWIPMOD_E)2U)
#define RWIPMOD_E_Auto_low             ((RWIPMOD_E)3U)
#define RWIPMOD_E_Wiper_Repair         ((RWIPMOD_E)4U)
#define RWIPMOD_E_Wiper_stall          ((RWIPMOD_E)6U)
#define RWIPMOD_E_Inter                ((RWIPMOD_E)7U)
#endif                                 /* RTW_HEADER_Rte_Type_h_ */


/* AUTOSAR Structure Types */
#ifndef DEFINED_TYPEDEF_FOR_SPU_Debug_STRUCT_
#define DEFINED_TYPEDEF_FOR_SPU_Debug_STRUCT_

typedef struct {
  UInt8 APP_ManuDebug;
  UInt8 APP_ManuFlow;
  UInt8 APP_ComitDebug;
  UInt8 APP_ComitFlow;
  UInt8 APP_ComitCmd;
  UInt8 APP_ComitTarPos;
  UInt8 APP_RecallDebug;
  UInt8 APP_RecallFlow;
  UInt8 APP_RecallCmd;
  UInt8 APP_RecallTarPos;
  UInt8 APP_LearnCmd;
  UInt8 APP_LearnFlow;
  UInt8 APP_MemDebug;
  UInt8 APP_MemFlow;
  UInt8 APP_MemLearDebug;
  UInt8 APP_MemLearFlow;
  UInt8 ENH_DebugSts;
  UInt8 ENH_DebugFlow;
  UInt8 ENH_ManuCmd;
  UInt8 ENH_AutoCmd;
  UInt8 ENH_LearnCmd;
  SInt16 EHN_TargePos;
  UInt8 ATM_DebugSts;
  UInt8 ATM_StopReason;
  UInt8 ATM_MotorDebug;
  UInt8 ATM_LearnDebug;
  UInt8 ATM_HallLost;
} SPU_Debug_STRUCT;

#endif

#ifndef DEFINED_TYPEDEF_FOR_TURN_CurrWarning_struct_
#define DEFINED_TYPEDEF_FOR_TURN_CurrWarning_struct_

typedef struct {
  Boolean FLCurrWarning;
  Boolean FRCurrWarning;
  Boolean RLCurrWarning;
  Boolean RRCurrWarning;
} TURN_CurrWarning_struct;

#endif

#ifndef DEFINED_TYPEDEF_FOR_TURN_Fault_Struct_
#define DEFINED_TYPEDEF_FOR_TURN_Fault_Struct_

typedef struct {
  Boolean FLTurnLighFault;
  Boolean FRTurnLighFault;
  Boolean RLTurnLighFault;
  Boolean RRTurnLighFault;
} TURN_Fault_Struct;

#endif


#ifndef DEFINED_TYPEDEF_FOR_TURN_ShortOutPower_struct_
#define DEFINED_TYPEDEF_FOR_TURN_ShortOutPower_struct_

typedef struct {
  Boolean FLShortOutPower;
  Boolean FRShortOutPower;
  Boolean RLShortOutPower;
  Boolean RRShortOutPower;
} TURN_ShortOutPower_struct;

#endif


#ifndef DEFINED_TYPEDEF_MIR_DEBUG_STRUCT_
#define DEFINED_TYPEDEF_MIR_DEBUG_STRUCT_
typedef struct {
    UInt8 ManulaAdjDebug;
    UInt8 LTrgDebug;
    UInt8 RTrgDebug;
    UInt8 MemoryDebug;
    UInt8 TiltDebug;
    UInt8 MirFlipStepDebug_path;
    UInt8 MirFlipStepR;
    UInt8 SaveDebug_Mode;
    UInt8 SaveID;
    UInt8 FoldTry;
    UInt8 FoldDebug;
    UInt8 SO_ArbDebug;
    UInt8 LDebug_AutoStep;
    UInt8 RDebug_AutoStep;
} MIR_DEBUG_STRUCT;
#endif

#ifndef DEFINED_TYPEDEF_MIR_EPRMSts_STRUCT_
#define DEFINED_TYPEDEF_MIR_EPRMSts_STRUCT_
typedef struct {
    UInt8 SaveDrvMode_VtcPos1;
    UInt8 SaveDrvMode_HrzPos1;
    UInt8 SaveDrvMode_VtcPos2;
    UInt8 SaveDrvMode_HrzPos2;
    UInt8 SaveDrvMode_VtcPos3;
    UInt8 SaveDrvMode_HrzPos3;
    UInt8 SaveDrvMode_VtcPos4;
    UInt8 SaveDrvMode_HrzPos4;
    UInt8 SaveDrvMode_VtcPos5;
    UInt8 SaveDrvMode_HrzPos5;
    UInt8 SaveDrvMode_VtcPos6;
    UInt8 SaveDrvMode_HrzPos6;
    UInt8 SaveDrvMode_VtcPos7;
    UInt8 SaveDrvMode_HrzPos7;
    UInt8 SaveDrvMode_VtcPos8;
    UInt8 SaveDrvMode_HrzPos8;
    UInt8 SaveDrvMode_VtcPos9;
    UInt8 SaveDrvMode_HrzPos9;
    UInt8 SaveDrvMode_VtcPos10;
    UInt8 SaveDrvMode_HrzPos10;
} MIR_EPRMSts_STRUCT;
#endif

#ifndef DEFINED_TYPEDEF_MIR_EPRMTiltSts_STRUCT_
#define DEFINED_TYPEDEF_MIR_EPRMTiltSts_STRUCT_
typedef struct {
    UInt8 SaveTiltMode_VtcPos1;
    UInt8 SaveTiltMode_HrzPos1;
    UInt8 SaveTiltMode_VtcPos2;
    UInt8 SaveTiltMode_HrzPos2;
    UInt8 SaveTiltMode_VtcPos3;
    UInt8 SaveTiltMode_HrzPos3;
    UInt8 SaveTiltMode_VtcPos4;
    UInt8 SaveTiltMode_HrzPos4;
    UInt8 SaveTiltMode_VtcPos5;
    UInt8 SaveTiltMode_HrzPos5;
    UInt8 SaveTiltMode_VtcPos6;
    UInt8 SaveTiltMode_HrzPos6;
    UInt8 SaveTiltMode_VtcPos7;
    UInt8 SaveTiltMode_HrzPos7;
    UInt8 SaveTiltMode_VtcPos8;
    UInt8 SaveTiltMode_HrzPos8;
    UInt8 SaveTiltMode_VtcPos9;
    UInt8 SaveTiltMode_HrzPos9;
    UInt8 SaveTiltMode_VtcPos10;
    UInt8 SaveTiltMode_HrzPos10;
} MIR_EPRMTiltSts_STRUCT;
#endif

#ifndef DEFINED_TYPEDEF_MIR_ADJ_STRUCT_
#define DEFINED_TYPEDEF_MIR_ADJ_STRUCT_
typedef struct {
    UInt8 L_Mir_Current_VtcPos;
    UInt8 L_Mir_Current_HrzPos;
} MIR_ADJ_STRUCT;
#endif

#ifndef DEFINED_TYPEDEF_SEAT_DrvOutputs_STRUCT_
#define DEFINED_TYPEDEF_SEAT_DrvOutputs_STRUCT_
typedef struct {
    UInt8 LearnSts;
    UInt8 LearnResult;
    UInt16 CorrectPos_Zero;
    Boolean CorrectPos_Max;
    int16_T ManualCorrectPos;
} SEAT_DrvOutputs_STRUCT;
#endif

#ifndef DEFINED_TYPEDEF_SDU_BasalToEE_STRUCT_
#define DEFINED_TYPEDEF_SDU_BasalToEE_STRUCT_
typedef struct {
    int16_T CurrentPos;
    int16_T MaxPos;
    UInt8 LearnSts;
    int16_T WelcomPos;
    int16_T MeyID1Pos;
    int16_T MeyID2Pos;
    int16_T MeyID3Pos;
    int16_T MeyID4Pos;
    int16_T MeyID5Pos;
    int16_T MeyID6Pos;
    int16_T MeyID7Pos;
    int16_T MeyID8Pos;
    int16_T MeyID9Pos;
    int16_T MeyID10Pos;
    int16_T BeforeEnterPos;
} SDU_BasalToEE_STRUCT;
#endif

















#endif


