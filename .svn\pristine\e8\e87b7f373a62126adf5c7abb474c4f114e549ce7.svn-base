@echo off
::setlocal
set src_folder = "%cd%

cd  ..\..\ICAR05_L_OT_Model\
call collect_autosar_SIL.bat
::wait 10S
echo curr filedir0:%cd%
ping 127.0.0.1 -n 10 >nul
cd A_Code_Integration\arxml
echo curr filedir1:%cd%


del "*_datatype.arxml"
del "*implementation.arxml"
del "DEBUGOUT.arxml"
del "*OUP.arxml"

cd ..\

echo curr filedir11111:%cd%
ping 127.0.0.1 -n 5 >nul
call ..\..\ArxmlTool_V_1_0_9\ArxmlTool.exe

::wait 10S
::ping 127.0.0.1 -n 10 >nul
echo curr filedir2:%cd%
cd ..\..\ICAR05_L_OT_ModelTest\build
echo curr filedir2222:%cd%
%copy /y  ..\..\ArxmlTool_V_1_0_9\RTE\Rte_*.h  ..\src
%copy /y  ..\..\ArxmlTool_V_1_0_9\RTE\RTE.c  ..\testcase
%copy /y  ..\..\ArxmlTool_V_1_0_9\RTE\RTE.h  ..\testcase
::wait 3S
ping 127.0.0.1 -n 3 >nul
cd ..\..\ICAR05_L_OT_ModelTest\build
%copy /y ..\..\ICAR05_L_OT_Model\CUSTOM_ENUM\enum.h ..\src
::%copy /y ..\..\ICAR05_L_OT_Model\A_Code_Integration\Rte_Type.h ..\src

%copy /y ..\..\ICAR05_L_OT_Model\A_Code_Integration\*.c ..\src
%copy /y ..\..\ICAR05_L_OT_Model\A_Code_Integration\*.h ..\src

::AUTH单独使用的文件
::%copy /y ..\..\ICAR05_L_OT_Model\AUTH\RKE_Decrypt.c ..\src
::%copy /y ..\..\ICAR05_L_OT_Model\AUTH\RKE_Decrypt_H.h ..\src


del ..\..\ICAR05_L_OT_ModelTest\src\SC_data.c
del ..\..\ICAR05_L_OT_ModelTest\src\ALM_data.c
del ..\..\ICAR05_L_OT_ModelTest\src\MIR_data.c
::del ..\..\ICAR05_L_OT_ModelTest\src\SDHU_data.c
del ..\..\ICAR05_L_OT_ModelTest\src\SPU_data.c

cd ..\..\ICAR05_L_OT_ModelTest\build

::echo curr filedir4:%cd%
cmake -G"MinGW Makefiles" ..
mingw32-make
ICAR05_L_OT_ModelTest --gtest_output="xml:report.xml"
del .\CMakeFiles\ICAR05_L_OT_ModelTest.dir\testcase\*.gcno
del .\CMakeFiles\ICAR05_L_OT_ModelTest.dir\testcase\*.gcda
cd..
gcovr -r . --exclude-directories '\./testcase' --xml -o coverage.xml
%gcovr -r . --html --html-details -o result.html%
pause