/*
 * File: look2_iu8lu32n24_binlcse.c
 *
 * Code generated for Simulink model 'Enh_Window_L'.
 *
 * Model version                  : 1.29
 * Simulink Coder version         : 9.7 (R2022a) 13-Nov-2021
 * C/C++ source code generated on : Thu May 25 16:42:21 2023
 */

#include "look2_iu8lu32n24_binlcse.h"
#include "rtwtypes.h"

uint8 look2_iu8lu32n24_binlcse(uint8 u0, uint8 u1, const uint8 bp0[],
  const uint8 bp1[], const uint8 table[], const uint32 maxIndex[],
  uint32 stride)
{
  uint32 bpIndices[2];
  uint32 fractions[2];
  uint32 bpIdx;
  uint32 frac;
  uint32 iLeft;
  uint8 bpLeftVar;
  uint8 y;
  uint8 yL_0d0;
  uint8 yL_1d;

  /* Column-major Lookup 2-D
     Search method: 'binary'
     Use previous index: 'off'
     Interpolation method: 'Linear point-slope'
     Extrapolation method: 'Clip'
     Use last breakpoint for index at or above upper limit: 'off'
     Remove protection against out-of-range input in generated code: 'off'
     Rounding mode: 'simplest'
   */
  /* Prelookup - Index and Fraction
     Index Search method: 'binary'
     Extrapolation method: 'Clip'
     Use previous index: 'off'
     Use last breakpoint for index at or above upper limit: 'off'
     Remove protection against out-of-range input in generated code: 'off'
     Rounding mode: 'simplest'
   */
  if (u0 <= bp0[0U]) {
    iLeft = 0U;
    frac = 0U;
  } else if (u0 < bp0[maxIndex[0U]]) {
    /* Binary Search */
    bpIdx = maxIndex[0U] >> 1U;
    iLeft = 0U;
    frac = maxIndex[0U];
    while (frac - iLeft > 1U) {
      if (u0 < bp0[bpIdx]) {
        frac = bpIdx;
      } else {
        iLeft = bpIdx;
      }

      bpIdx = (frac + iLeft) >> 1U;
    }

    bpLeftVar = bp0[iLeft];
    frac = ((uint32)(uint8)((uint32)u0 - bpLeftVar) << 24) / (uint8)
      ((uint32)bp0[iLeft + 1U] - bpLeftVar);
  } else {
    iLeft = maxIndex[0U] - 1U;
    frac = 16777216U;
  }

  fractions[0U] = frac;
  bpIndices[0U] = iLeft;

  /* Prelookup - Index and Fraction
     Index Search method: 'binary'
     Extrapolation method: 'Clip'
     Use previous index: 'off'
     Use last breakpoint for index at or above upper limit: 'off'
     Remove protection against out-of-range input in generated code: 'off'
     Rounding mode: 'simplest'
   */
  if (u1 <= bp1[0U]) {
    iLeft = 0U;
    frac = 0U;
  } else if (u1 < bp1[maxIndex[1U]]) {
    /* Binary Search */
    bpIdx = maxIndex[1U] >> 1U;
    iLeft = 0U;
    frac = maxIndex[1U];
    while (frac - iLeft > 1U) {
      if (u1 < bp1[bpIdx]) {
        frac = bpIdx;
      } else {
        iLeft = bpIdx;
      }

      bpIdx = (frac + iLeft) >> 1U;
    }

    bpLeftVar = bp1[iLeft];
    frac = ((uint32)(uint8)((uint32)u1 - bpLeftVar) << 24) / (uint8)
      ((uint32)bp1[iLeft + 1U] - bpLeftVar);
  } else {
    iLeft = maxIndex[1U] - 1U;
    frac = 16777216U;
  }

  /* Column-major Interpolation 2-D
     Interpolation method: 'Linear point-slope'
     Use last breakpoint for index at or above upper limit: 'off'
     Rounding mode: 'simplest'
     Overflow mode: 'wrapping'
   */
  bpIdx = iLeft * stride + bpIndices[0U];
  bpLeftVar = table[bpIdx + 1U];
  yL_0d0 = table[bpIdx];
  if (bpLeftVar >= yL_0d0) {
    yL_1d = (uint8)((uint32)(uint8)(((uint8)((uint32)bpLeftVar -
      yL_0d0) * fractions[0U]) >> 24) + yL_0d0);
  } else {
    yL_1d = (uint8)((uint32)yL_0d0 - (uint8)(((uint8)((uint32)yL_0d0 -
      bpLeftVar) * fractions[0U]) >> 24));
  }

  bpIdx += stride;
  bpLeftVar = table[bpIdx + 1U];
  yL_0d0 = table[bpIdx];
  if (bpLeftVar >= yL_0d0) {
    bpLeftVar = (uint8)((uint32)(uint8)(((uint8)((uint32)bpLeftVar -
      yL_0d0) * fractions[0U]) >> 24) + yL_0d0);
  } else {
    bpLeftVar = (uint8)((uint32)yL_0d0 - (uint8)(((uint8)((uint32)
      yL_0d0 - bpLeftVar) * fractions[0U]) >> 24));
  }

  if (bpLeftVar >= yL_1d) {
    y = (uint8)((uint32)(uint8)(((uint8)((uint32)bpLeftVar - yL_1d) *
      frac) >> 24) + yL_1d);
  } else {
    y = (uint8)((uint32)yL_1d - (uint8)(((uint8)((uint32)yL_1d -
      bpLeftVar) * frac) >> 24));
  }

  return y;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
