/*
 * File: PWNL_data.c
 *
 * Code generated for Simulink model 'PWNL'.
 *
 * Model version                  : 1.3
 * Simulink Coder version         : 9.7 (R2022a) 13-Nov-2021
 * C/C++ source code generated on : Tue Aug 19 11:16:41 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Intel->x86-64 (Windows64)
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "PWNL.h"

/* Invariant block signals (default storage) */
const ConstB_PWNL_T PWNL_ConstB = {
  1                                    /* '<S36>/Compare' */
};

/* Constant parameters (default storage) */
const ConstP_PWNL_T PWNL_ConstP = {
  /* Pooled Parameter (Expression: uint8(reshape([
     0 1 2 3 4 0 6 7 8 9 0 11 12 13 0 0 16 17 18 19 0 21 22 23 24 0
     1 1 2 3 4 0 6 7 8 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
     2 0 0 0 0 0 0 0 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
     3 1 2 3 4 0 6 7 8 9 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3
     4 0 0 0 0 0 0 0 0 0 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4
     0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
     6 1 2 3 4 6 6 7 8 9 0 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6
     7 0 0 0 0 7 0 0 0 0 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7
     8 1 2 3 4 8 6 7 8 9 0 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8
     9 0 0 0 0 9 0 0 0 0 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9
     0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
     11 1 2 3 4 11 6 7 8 9 11 11 12 13 0 0 11 11 11 11 11 11 11 11 11 11
     12 1 2 3 4 12 6 7 8 9 12 0 0 13 0 12 12 12 12 12 12 12 12 12 12 12
     13 1 2 3 4 13 6 7 8 9 13 11 12 13 0 0 13 13 13 13 13 13 13 13 13 13
     0 1 2 3 4 0 6 7 8 9 0 11 12 13 0 0 0 0 0 0 0 0 0 0 0 0
     0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
     16 0 0 0 0 16 0 0 0 0 16 0 0 0 0 16 16 17 18 19 0 21 22 23 24 16
     17 0 0 0 0 17 0 0 0 0 17 0 0 0 0 17 16 17 18 19 0 21 22 23 24 17
     18 0 0 0 0 18 0 0 0 0 18 0 0 0 0 18 16 17 18 19 0 21 22 23 24 18
     19 0 0 0 0 19 0 0 0 0 19 0 0 0 0 19 16 17 18 19 0 21 22 23 24 19
     0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
     21 0 0 0 0 21 0 0 0 0 21 0 0 0 0 21 16 17 18 19 21 21 22 23 24 0
     22 0 0 0 0 22 0 0 0 0 22 0 0 0 0 22 16 17 18 19 22 21 22 23 24 22
     23 0 0 0 0 23 0 0 0 0 23 0 0 0 0 23 16 17 18 19 23 21 22 23 24 0
     24 0 0 0 0 24 0 0 0 0 24 0 0 0 0 24 16 17 18 19 24 21 22 23 24 24
     0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
     ],26,26));
     )
   * Referenced by:
   *   '<S52>/2-D Lookup FL_Table(PriorityTable)5'
   *   '<S53>/2-D Lookup FL_Table(PriorityTable)5'
   */
  { 0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 16U, 17U,
    18U, 19U, 0U, 21U, 22U, 23U, 24U, 0U, 1U, 1U, 0U, 1U, 0U, 0U, 1U, 0U, 1U, 0U,
    0U, 1U, 1U, 1U, 1U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 2U, 2U, 0U,
    2U, 0U, 0U, 2U, 0U, 2U, 0U, 0U, 2U, 2U, 2U, 2U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
    0U, 0U, 0U, 0U, 3U, 3U, 0U, 3U, 0U, 0U, 3U, 0U, 3U, 0U, 0U, 3U, 3U, 3U, 3U,
    0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 4U, 4U, 0U, 4U, 0U, 0U, 4U, 0U,
    4U, 0U, 0U, 4U, 4U, 4U, 4U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
    0U, 0U, 0U, 0U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 16U, 17U, 18U,
    19U, 0U, 21U, 22U, 23U, 24U, 0U, 6U, 6U, 0U, 6U, 0U, 0U, 6U, 0U, 6U, 0U, 0U,
    6U, 6U, 6U, 6U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 7U, 7U, 0U, 7U,
    0U, 0U, 7U, 0U, 7U, 0U, 0U, 7U, 7U, 7U, 7U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
    0U, 0U, 0U, 8U, 8U, 0U, 8U, 0U, 0U, 8U, 0U, 8U, 0U, 0U, 8U, 8U, 8U, 8U, 0U,
    0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 9U, 9U, 0U, 9U, 0U, 0U, 9U, 0U, 9U,
    0U, 0U, 9U, 9U, 9U, 9U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U,
    2U, 3U, 4U, 0U, 0U, 7U, 0U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 16U, 17U, 18U,
    19U, 0U, 21U, 22U, 23U, 24U, 0U, 11U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U,
    11U, 0U, 11U, 11U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 12U, 1U, 2U,
    3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 12U, 0U, 12U, 12U, 0U, 0U, 0U, 0U, 0U, 0U,
    0U, 0U, 0U, 0U, 0U, 13U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 13U, 13U,
    13U, 13U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U, 2U, 3U, 4U, 0U,
    6U, 7U, 8U, 9U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
    0U, 0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 0U, 12U, 0U, 0U, 0U, 16U,
    17U, 18U, 19U, 0U, 21U, 22U, 23U, 24U, 0U, 16U, 1U, 2U, 3U, 4U, 0U, 6U, 7U,
    8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 16U, 16U, 16U, 16U, 0U, 16U, 16U, 16U,
    16U, 0U, 17U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U,
    17U, 17U, 17U, 17U, 0U, 17U, 17U, 17U, 17U, 0U, 18U, 1U, 2U, 3U, 4U, 0U, 6U,
    7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 18U, 18U, 18U, 18U, 0U, 18U, 18U, 18U,
    18U, 0U, 19U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U,
    19U, 19U, 19U, 19U, 0U, 19U, 19U, 19U, 19U, 0U, 0U, 1U, 2U, 3U, 4U, 0U, 6U,
    7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 21U, 22U, 23U,
    24U, 0U, 21U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U,
    21U, 21U, 21U, 21U, 0U, 21U, 21U, 21U, 21U, 0U, 22U, 1U, 2U, 3U, 4U, 0U, 6U,
    7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 22U, 22U, 22U, 22U, 0U, 22U, 22U, 22U,
    22U, 0U, 23U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U,
    23U, 23U, 23U, 23U, 0U, 23U, 23U, 23U, 23U, 0U, 24U, 1U, 2U, 3U, 4U, 0U, 6U,
    7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U, 24U, 24U, 24U, 24U, 0U, 24U, 24U, 24U,
    24U, 0U, 0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 0U, 0U,
    16U, 17U, 18U, 19U, 0U, 0U, 22U, 0U, 24U, 0U }
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
