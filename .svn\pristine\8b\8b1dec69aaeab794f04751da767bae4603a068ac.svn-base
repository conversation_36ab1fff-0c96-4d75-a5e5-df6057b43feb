/*
 * File: look2_iu8lu32n24_binlcse.h
 *
 * Code generated for Simulink model 'Enh_Window_L'.
 *
 * Model version                  : 1.29
 * Simulink Coder version         : 9.7 (R2022a) 13-Nov-2021
 * C/C++ source code generated on : Thu May 25 16:42:21 2023
 */

#ifndef RTW_HEADER_look2_iu8lu32n24_binlcse_h_
#define RTW_HEADER_look2_iu8lu32n24_binlcse_h_
#include "rtwtypes.h"

extern uint8 look2_iu8lu32n24_binlcse(uint8 u0, uint8 u1, const uint8
  bp0[], const uint8 bp1[], const uint8 table[], const uint32 maxIndex[],
  uint32 stride);

#endif                              /* RTW_HEADER_look2_iu8lu32n24_binlcse_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
