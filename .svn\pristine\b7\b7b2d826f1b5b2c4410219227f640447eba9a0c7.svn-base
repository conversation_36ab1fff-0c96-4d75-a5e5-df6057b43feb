#include "gtest/gtest.h"
#include "TEST_API.h"
#include <stdio.h>
#include <iostream>
#include <string>
#include <math.h>
#include "enum.h"
using namespace std;

int nummmmmmmmmmm=0;

static int TEST_TEMP(int Test_1,string Test_2,boolean_T &Test_3,int time)
{
    int temp=Test_1;
    int num=0;
    for(int i=1;i<=time;i++)
    {
        TestDLKStep(1);
        VeINP_CAN_CrashSts_sig=0;
        if(temp!=Test_3)
        {
            temp=Test_3;
            num++;
            cout<<"经过的时间为："<<i-nummmmmmmmmmm<<"ticks后"<<Test_2<<"的值变化为："<<temp<<"\n";
            nummmmmmmmmmm=i;
        }
    }
    nummmmmmmmmmm=0;
    cout<<"次数"<<num/2<<endl;
    return num/2;
}

static void Crash()
{
    TestDLKStep(2);
    VeINP_CAN_CrashSts_sig=0;
    EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
    EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
    for(int i=0;i<6;i++)
    {
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        TestDLKStep(81);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
    }
    TestDLKStep(20);
    EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
    EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
}

namespace
{
    TEST(DLK,SI_TC_DLK_400)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        //
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VeOUT_CMS_ZCULCarMode_sig=1;
        TestDLKStep(110);

        //观测DLK输出是否正确

        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_401)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_402)
    {
        TEST_DLKINIT();
        //档位在P档、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
//        TEST_TEMP(0,"VeOUT_DLK_ZCULLockCmdSource_sig",VeOUT_DLK_ZCULLockCmdSource_sig,1000);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(92);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_403)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_404)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_405)
    {

        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 30);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_406)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_407)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 30);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 30);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 7);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_408)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_409)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_410)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_411)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_412)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_413)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_414)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_415)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_416)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_417)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_418)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_419)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_420)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_421)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_422)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_423)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_424)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_425)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_426)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_427)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_428)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_429)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门关打开、
        //远离闭锁功能开启
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 30);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_430)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        //
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_431)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_432)
    {
        TEST_DLKINIT();
        //档位在P档、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_433)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_434)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_435)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_436)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_437)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_438)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_439)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_440)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_441)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_442)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_443)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_444)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_445)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_446)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_447)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_448)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_449)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_450)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_451)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_452)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_453)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_454)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_455)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_456)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_457)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_458)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_459)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门关打开、
        //远离闭锁功能关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        TestDLKStep(110);

        //观测DLK输出是否正确
        GTEST_SKIP();
        VeINP_CAN_DKMBLECommandReq_sig=4;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_460)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=110;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
//        TEST_TEMP(0,"VeOUT_DLK_ZCULLockCmdSource_sig",VeOUT_DLK_ZCULLockCmdSource_sig,1000);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_461)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_462)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_463)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_464)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_465)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_466)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_467)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_468)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_469)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_470)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_471)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_472)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_473)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_474)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_475)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_476)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_477)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_478)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_479)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_480)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_481)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_482)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_483)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_484)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_485)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_486)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_487)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbINP_HWA_IGNFeedBackIN_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_488)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_489)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_490)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_491)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_492)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_493)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_494)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_495)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_496)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_497)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_498)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_499)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_500)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_501)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_502)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_503)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_504)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_505)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_506)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_507)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_508)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_509)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_510)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_511)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_512)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_513)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 19);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 19);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 134);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_514)
    {
        TEST_DLKINIT();
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        //档位在P档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 18);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 18);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_515)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_516)
    {
        TEST_DLKINIT();
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        //档位在P档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 18);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 18);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_517)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 18);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 18);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_518)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_519)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 18);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 18);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_520)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_521)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 18);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 18);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_522)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 6);
        TestDLKStep(84);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 19);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 19);
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_523)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_524)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_525)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_526)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_527)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_528)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_529)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_530)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_531)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_532)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_533)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_534)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_535)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_536)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_537)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_538)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_539)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_540)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_541)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_542)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_543)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 18);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_544)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_545)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_546)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_547)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_548)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_549)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_550)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_551)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_552)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_553)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_554)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_555)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_556)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_557)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_558)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_559)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_560)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_561)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_562)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_563)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_564)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_565)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_566)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_567)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_568)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_569)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_570)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_571)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_572)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_573)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_574)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_575)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_576)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_577)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_578)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_579)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_580)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_581)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_582)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_583)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_584)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_585)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_586)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_587)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_588)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_589)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_590)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_591)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_592)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_593)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_594)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_595)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_596)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_597)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 56);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 56);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 137);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_598)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 55);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_599)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_600)
    {
        TEST_DLKINIT();
        //档位在P档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 55);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_601)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(84);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_602)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_603)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 55);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(484);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_604)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_605)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);
        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(84);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_606)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 9);
        TestDLKStep(84);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 20);
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_607)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_608)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_609)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_610)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_611)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_612)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_613)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_614)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_615)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_616)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_617)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_618)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_619)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_620)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_621)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_622)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_623)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_624)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_625)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_626)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_627)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=0;
        TestDLKStep(110);

        //观测DLK的输出值
        VeINP_CAN_TCPDriverDoorLockControl5G_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 55);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_628)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=0;
        VbINP_CAN_ICMOverSpeedLockCarReq_flg=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VbINP_CAN_ICMOverSpeedLockCarReq_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULOverSpeedLockCarSts_flg, 1);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULOverSpeedLockCarSts_flg, 1);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VbOUT_DLK_ZCULOverSpeedLockCarSts_flg, 1);
    }

    TEST(DLK,SI_TC_DLK_629)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_CAN_ICMOverSpeedLockCarReq_flg=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VbINP_CAN_ICMOverSpeedLockCarReq_flg=1;
        TestDLKStep(1);
        TestDLKStep(1);
        TestDLKStep(1);
        TestDLKStep(100);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULOverSpeedLockCarSts_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_630)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(499);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);

    }

    TEST(DLK,SI_TC_DLK_631)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=16;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(499);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_632)
    {

        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=15;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(16);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_633)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=0;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_634)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁闭锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_635)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁闭锁
        //电源OFF、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_636)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁闭锁
        //电源ON、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_637)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1、左前门锁闭锁
        //电源无效、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_638)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(499);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_639)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=16;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(499);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 1);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_640)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=15;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(16);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_641)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁解锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=0;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_642)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁闭锁
        //电源RUN、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_643)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁闭锁
        //电源OFF、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_644)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁闭锁
        //电源ON、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_645)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0、左前门锁闭锁
        //电源无效、四门关闭
        VbINP_EPRM_ZCULOverSpeedLockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeOUT_SP_EspVehSpd_kmh=30;
        TestDLKStep(3);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(12);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_646)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)0
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=0;
        VbINP_CAN_ICMStopUnlockCarReq_flg=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VbINP_CAN_ICMStopUnlockCarReq_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_ZCULStopUnlockCarStsToEE_flg, 1);
    }

    TEST(DLK,SI_TC_DLK_647)
    {
        TEST_DLKINIT();
        //行车落锁使能状态(EE读取)1
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_CAN_ICMStopUnlockCarReq_flg=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        VbINP_CAN_ICMStopUnlockCarReq_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_ZCULStopUnlockCarStsToEE_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_648)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、P档
        //
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_ICMOTASts_sig=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
        TestDLKStep(499);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_649)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)0、左前门锁闭锁
        //车速小于5且车速有效、P档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_650)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁解锁
        //车速小于5且车速有效、P档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_651)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于2且车速有效、P档
        //
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,129);
        TestDLKStep(499);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,129);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_652)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于4且车速有效、P档
        //
        VeOUT_PDU_PowerMode_sig=3;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,129);
        TestDLKStep(499);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,129);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_653)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于5且车速有效、P档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_654)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、P档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_655)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、无效档位
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=0;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_656)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、N档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=2;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_657)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、R档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=3;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_658)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、D档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=4;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_659)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速等于100且车速无效、错档
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_660)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、P档、安全带检测无效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_DrSbltSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_661)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、无档、安全带检测有效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=0;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_VCU1NActualGear_sig=1;
        TestDLKStep(3);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_662)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、N档、安全带检测有效
        //
        VeOUT_PDU_PowerMode_sig=4;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=2;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_VCU1NActualGear_sig=1;
        TestDLKStep(3);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(19);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(479);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_663)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、R档、安全带检测有效
        //
        VeOUT_PDU_PowerMode_sig=5;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=3;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_VCU1NActualGear_sig=1;
        TestDLKStep(3);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(19);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(479);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_664)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、D档、安全带检测有效
        //
        VeOUT_PDU_PowerMode_sig=6;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=4;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_VCU1NActualGear_sig=1;
        TestDLKStep(3);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(19);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(479);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_665)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、错档、安全带检测有效
        //
        VeOUT_PDU_PowerMode_sig=6;
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_VCU1NActualGear_sig=1;
        TestDLKStep(3);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(19);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(479);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x81);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0);
    }

    TEST(DLK,SI_TC_DLK_666)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、错档、安全带检测有效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_667)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、错档、安全带检测有效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=2;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_668)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、错档、安全带检测有效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=3;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_669)
    {
        TEST_DLKINIT();
        //驻车解锁使能状态(EE读取)1、左前门锁闭锁
        //车速小于5且车速有效、错档、安全带检测有效
        //
        VbINP_EPRM_ZCULStopUnlockCarStsFromEE_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_CAN_DrSbltSts_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=15;
        VbINP_CAN_DrSbltSts_flg=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_DrSbltSts_flg=4;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_670)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_671)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_672)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VeINP_CAN_CrashSts_sig=3;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_673)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VeINP_CAN_CrashSts_sig=3;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_674)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(100);
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);

    }

    TEST(DLK,SI_TC_DLK_675)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(299);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(99);
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);

    }

    TEST(DLK,SI_TC_DLK_676)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S外
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_677)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S外
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_678)
    {
        TEST_DLKINIT();
        //电源ON
        //检测门锁的实际输出
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(130);
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);

    }

    TEST(DLK,SI_TC_DLK_679)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);

        EXPECT_EQ(VmOUT_DLK_LockSrc_enum,0);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
    }

    TEST(DLK,SI_TC_DLK_680)
    {
//        setDebugOpen();
//        int i=110;
        a:TEST_DLKINIT();
        Test_CycleTestInit();
        Test_CycleTestRegister(&VbOUT_DLK_DoorFLUnlock_flg,1,1);
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(100);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
//        int nummlll=TEST_TEMP(0,"VbOUT_DLK_DoorFLUnlock_flg",VbOUT_DLK_DoorFLUnlock_flg,630);
//        Crash();

        TestDLKStep(10);
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(630);
        EXPECT_EQ(Test_GetCycles(1),7);


        TestDLKStep(200);
        VeINP_CAN_CrashSts_sig=3;

        TestDLKStep(1000);
        EXPECT_EQ(Test_GetCycles(1),14);

//        for(int i=0;i<100;i++)
//        {
//            TestDLKStep(10);
//        }

//        int numm=TEST_TEMP(0,"VbOUT_DLK_DoorFLUnlock_flg",VbOUT_DLK_DoorFLUnlock_flg,10000);
//        setDebugClose();
//        ++i;
//        if(numm==7)
//        {
//            cout<<i<<endl;
//            goto stop;
//        }
//        goto a;
//        stop:;
//        Crash();

    }

    TEST(DLK,SI_TC_DLK_681)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        Crash();
        TestDLKStep(10);
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(110);
        VeINP_CAN_CrashSts_sig=3;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_682)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_683)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_684)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_685)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_686)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(110);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);

    }

    TEST(DLK,SI_TC_DLK_687)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(299);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(99);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_688)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S外
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_689)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S外
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_690)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);

        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
    }

    TEST(DLK,SI_TC_DLK_691)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);

        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
    }

    TEST(DLK,SI_TC_DLK_692)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_693)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=0;
        TestDLKStep(10);
        VbINP_CAN_TCPCrashUnLockall_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_694)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_695)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_696)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_697)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_698)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S内
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(99);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_699)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S内
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(299);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(99);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_700)
    {
        TEST_DLKINIT();
        //电源ON到OFF1S外
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_701)
    {
        TEST_DLKINIT();
        //电源RUN到OFF1S外
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        TestDLKStep(210);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(101);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg, 0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_702)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);

        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
    }

    TEST(DLK,SI_TC_DLK_703)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        TestDLKStep(10);
        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_TCPFourDoorLockControl5G_sig=1;
        TestDLKStep(1);

        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 19);
    }

    TEST(DLK,SI_TC_DLK_704)
    {
        TEST_DLKINIT();
        //电源ON
        VeOUT_PDU_PowerMode_sig=1;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_705)
    {
        TEST_DLKINIT();
        //电源RUN
        VeOUT_PDU_PowerMode_sig=2;
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=0;
        VbINP_CAN_VCCMCrashValid_flg=0;
        TestDLKStep(10);
        VbINP_CAN_VCCMCrashSts_flg=1;
        VbINP_CAN_VCCMCrashValid_flg=1;
        Crash();

    }

    TEST(DLK,SI_TC_DLK_706)
    {
        TEST_DLKINIT();
        //热失控无效
        VeINP_CAN_BMS14FThermalRunawayFault_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_BMS14FThermalRunawayFault_sig=1;
//        TEST_TEMP(0,"VbOUT_DLK_DoorFLUnlock_flg",VbOUT_DLK_DoorFLUnlock_flg,1000);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x8C);
        for(int i=0;i<6;i++)
        {
            TestDLKStep(20);
            EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
            EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
            TestDLKStep(81);
            EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
            EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        }

    }

    TEST(DLK,SI_TC_DLK_707)
    {
        ////TODO
        TEST_DLKINIT();
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        //热失控无效
        VeINP_CAN_BMS14FThermalRunawayFault_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_BMS14FThermalRunawayFault_sig=1;
//        TEST_TEMP(0,"VbOUT_DLK_DoorFLUnlock_flg",VbOUT_DLK_DoorFLUnlock_flg,1000);
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x8C);
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        TestDLKStep(81);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        TestDLKStep(1000);
        VeOUT_PDU_PowerMode_sig=0;
        TestDLKStep(10);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VeINP_CAN_BMS14FThermalRunawayFault_sig=0;
        TestDLKStep(10);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,3);
        TestDLKStep(100);
        VeINP_CAN_BMS14FThermalRunawayFault_sig=1;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x8C);
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        TestDLKStep(81);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        TestDLKStep(1000);
        VeOUT_PDU_PowerMode_sig=1;
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);

        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        TEST_TEMP(0,"VeOUT_DLK_ZCULLockCmdSource_sig",VeOUT_DLK_ZCULLockCmdSource_sig,1000);
    }

    TEST(DLK,SI_TC_DLK_708)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_709)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_710)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_711)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_712)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_713)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_714)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_715)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_716)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 49);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_717)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 48);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_718)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 48);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_719)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_720)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_721)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_722)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_723)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_724)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_725)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_726)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_727)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_728)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_729)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_730)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(497);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_731)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_732)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_733)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_734)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_735)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_736)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_737)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition0_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_738)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_739)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_740)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_741)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_742)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_743)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition1_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_744)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_745)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_746)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_747)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_748)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_749)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition2_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_750)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_751)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且小于5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_752)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速0
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_753)
    {
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速5
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_754)
    {
        GTEST_SKIP();
        TEST_DLKINIT();
        //
        //四门关闭
        //车速有效且车速4
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 20);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 20);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 138);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_755)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙有效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=4;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_756)
    {
        TEST_DLKINIT();
        //
        //四门打开
        //车速有效且车速2
        //钥匙无效
        //后背门打开
        VbOUT_SP_4DoorAjar_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBKEKeyPosition3_sig=0;
        VeINP_CAN_DKMBKEKeyPosition2_sig=0;
        VeINP_CAN_DKMBKEKeyPosition1_sig=0;
        VeINP_CAN_DKMBKEKeyPosition0_sig=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_757)
    {
        TEST_DLKINIT();
        //
        //车速有效且车速2
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        GTEST_SKIP();
        //VeINP_CAN_ICMWashLiquidLidControl_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        //VeINP_CAN_ICMWashLiquidLidControl_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_758)
    {
        TEST_DLKINIT();
        //
        //车速有效且车速5
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        GTEST_SKIP();
        //VeINP_CAN_ICMWashLiquidLidControl_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        //VeINP_CAN_ICMWashLiquidLidControl_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_759)
    {
        TEST_DLKINIT();
        //
        //车速有效且车速0
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        GTEST_SKIP();
        //VeINP_CAN_ICMWashLiquidLidControl_sig=0;
        TestDLKStep(130);

        //观测DLK的输出是否符合预期
        //VeINP_CAN_ICMWashLiquidLidControl_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_760)
    {
        TEST_DLKINIT();
        //
        //车速无效且车速100
        VbOUT_SP_4DoorAjar_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        GTEST_SKIP();
        //VeINP_CAN_ICMWashLiquidLidControl_sig=0;
        TestDLKStep(110);

        //观测DLK的输出是否符合预期
        //VeINP_CAN_ICMWashLiquidLidControl_sig=1;
//        TEST_TEMP(0,"VbOUT_DLK_WashingLiquidLidUnlock_flg",VbOUT_DLK_WashingLiquidLidUnlock_flg,100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_761)
    {
        TEST_DLKINIT();
        //洗涤未闭锁
        GTEST_SKIP();
        //VeINP_CAN_ICMWashLiquidLidControl_sig=0;
        TestDLKStep(130);
        //观测DLK的输出是否符合预期
        //VeINP_CAN_ICMWashLiquidLidControl_sig=2;
//        TestDLKStep(1);
//        TestDLKStep(1);
//        TestDLKStep(1);
//        TestDLKStep(1);
//        TestDLKStep(13);
//        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48-->0);
//        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48-->0);
//        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 48-->0);
//        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4-->0);
    }

    TEST(DLK,SI_TC_DLK_762)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(10);
        for(int i=0;i<4;i++)
        {
            VeINP_LIN_DSPCentralButtSts_sig=1;
            VbINP_HWA_FLDoorLockStsFb_flg=0;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
            VeINP_LIN_DSPCentralButtSts_sig=0;
            TestDLKStep(100);
            VeINP_LIN_DSPCentralButtSts_sig=1;
            VbINP_HWA_FLDoorLockStsFb_flg=0;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
            VeINP_LIN_DSPCentralButtSts_sig=0;
            TestDLKStep(100);
        }
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_763)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(10);
        for(int i=0;i<4;i++)
        {
            VeINP_LIN_DSPCentralButtSts_sig=1;
            VbINP_HWA_FLDoorLockStsFb_flg=1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig=0;
            TestDLKStep(100);
            VeINP_LIN_DSPCentralButtSts_sig=1;
            VbINP_HWA_FLDoorLockStsFb_flg=1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig=0;
            TestDLKStep(100);
        }
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        ////触发热保护
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        ////1.02s

        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        ////2.03s
        for(int i=0;i<12;i++)
        {
            TestDLKStep(100);
            VeINP_LIN_DSPCentralButtSts_sig=0;
            TestDLKStep(1);
            VeINP_LIN_DSPCentralButtSts_sig=1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        }
        ////14.274s
        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(1);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        TestDLKStep(1);
        ////允许再次触发
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);

    }

    TEST(DLK,SI_TC_DLK_764)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig = 1;
        VbOUT_PDU_PowerModeValid_flg = 1;
        VbOUT_SP_4DoorAjar_flg = 0;
        VbINP_HWA_FLDoorLockStsFb_flg = 0;
        VeINP_LIN_DSPCentralButtSts_sig = 0;
        VeINP_CAN_BMS14FThermalRunawayFault_sig=0;
        TestDLKStep(10);
        for (int i = 0; i < 4; i++)
        {
            VeINP_LIN_DSPCentralButtSts_sig = 1;
            VbINP_HWA_FLDoorLockStsFb_flg = 1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig = 0;
            TestDLKStep(100);
            VeINP_LIN_DSPCentralButtSts_sig = 1;
            VbINP_HWA_FLDoorLockStsFb_flg = 1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig = 0;
            TestDLKStep(100);
        }
        VeINP_LIN_DSPCentralButtSts_sig = 1;
        VbINP_HWA_FLDoorLockStsFb_flg = 1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        ////触发热保护
        TestDLKStep(20);
        VeINP_CAN_BMS14FThermalRunawayFault_sig=3;
        TestDLKStep(2);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig,0x8C);
        for(int i=0;i<6;i++)
        {
            TestDLKStep(20);
            EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
            EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
            TestDLKStep(81);
            EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
            EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
        }
        TestDLKStep(5);
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);
        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        Crash();
    }

    TEST(DLK,SI_TC_DLK_765)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig = 1;
        VbOUT_PDU_PowerModeValid_flg = 1;
        VbOUT_SP_4DoorAjar_flg = 0;
        VbINP_HWA_FLDoorLockStsFb_flg = 0;
        VeINP_LIN_DSPCentralButtSts_sig = 0;
        VeINP_CAN_BMS14FThermalRunawayFault_sig=3;
        TestDLKStep(10);
        for (int i = 0; i < 4; i++)
        {
            VeINP_LIN_DSPCentralButtSts_sig = 1;
            VbINP_HWA_FLDoorLockStsFb_flg = 1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig = 0;
            TestDLKStep(100);
            VeINP_LIN_DSPCentralButtSts_sig = 1;
            VbINP_HWA_FLDoorLockStsFb_flg = 1;
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
            EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
            TestDLKStep(1);
            EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
            VeINP_LIN_DSPCentralButtSts_sig = 0;
            TestDLKStep(100);
        }
        VeINP_LIN_DSPCentralButtSts_sig = 1;
        VbINP_HWA_FLDoorLockStsFb_flg = 1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        ////触发热保护
        TestDLKStep(5);
        VeINP_CAN_CrashSts_sig=0;
        TestDLKStep(130);
        //检测门锁的实际输出
        VeINP_CAN_CrashSts_sig=3;
        Crash();
    }

    TEST(DLK,SI_TC_DLK_766)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(10);

        //观测DLK的输出是否符合预期
        VmOUT_ALM_DLKReq_enum=FOLD;
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_767)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(10);

        //观测DLK的输出是否符合预期
        VmOUT_ALM_DLKReq_enum=DISARM_UNLOCK;
        TEST_TEMP(0,"VmOUT_DLK_LockSrc_enum",VmOUT_DLK_LockSrc_enum,1000);
//        TestDLKStep(1);
//        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, DISARM_EVENT);
//        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, DISARM_EVENT);
//        TestDLKStep(20);
//        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
    }

    TEST(DLK,SI_TC_DLK_768)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        TestDLKStep(10);

        //观测DLK的输出是否符合预期
        VmOUT_ALM_DLKReq_enum=RELOCK;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, ALM_RELOCK);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, ALM_RELOCK);
        TestDLKStep(20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
    }

    TEST(DLK,SI_TC_DLK_769)
    {
        TEST_DLKINIT();
        VeOUT_CMS_ZCULPEPSExitVehAntiTheftEnableSts_sig=2;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        TestDLKStep(10);
        VbINP_CAN_ICMPollingLockCarRed_flg=2;
        TestDLKStep(10);
        //观测DLK的输出是否符合预期
        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, POLLING_LOCK);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, POLLING_LOCK);
        TestDLKStep(20);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
    }

    TEST(DLK,SI_TC_DLK_770)
    {
        TEST_DLKINIT();
        VmOUT_ALM_AlarmState_enum=6;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_VCU1FActualGear_flg=1;
        TestDLKStep(20);
        VmOUT_ALM_DLKReq_enum=FOLD;
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_771)
    {
        TEST_DLKINIT();
        VmOUT_ALM_AlarmState_enum=2;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VbINP_CAN_VCU1FActualGear_flg=1;
        TestDLKStep(20);
        VmOUT_ALM_DLKReq_enum=FOLD;
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_772)
    {
        TEST_DLKINIT();
        VeOUT_PDU_PowerMode_sig=2;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(10);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULDrvDoorLockSts_sig,1);////解锁
        EXPECT_EQ(VbOUT_DLK_WashingLiquidLidUnlock_flg,1);
        TestDLKStep(40);
        EXPECT_EQ(VeOUT_DLK_ZCULDrvDoorLockSts_sig,1);////解锁
        EXPECT_EQ(VbOUT_DLK_WashingLiquidLidUnlock_flg,0);
    }

    TEST(DLK,SI_TC_DLK_773)
    {
        TEST_DLKINIT();
        VeOUT_PDU_PowerMode_sig=2;
        VeOUT_SP_EspVehSpd_kmh=4;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(10);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULDrvDoorLockSts_sig,0);
        EXPECT_EQ(VbOUT_DLK_WashingLiquidLidLock_flg,1);
        TestDLKStep(40);
        EXPECT_EQ(VeOUT_DLK_ZCULDrvDoorLockSts_sig,0);
        EXPECT_EQ(VbOUT_DLK_WashingLiquidLidLock_flg,0);
        TEST_TEMP(0,"VbOUT_DLK_WashingLiquidLidUnlock_flg",VbOUT_DLK_WashingLiquidLidUnlock_flg,1000);
    }


}