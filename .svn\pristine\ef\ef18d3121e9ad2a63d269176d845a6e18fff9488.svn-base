#include "gtest/gtest.h"
#include "RTE.h"
#include "TEST_API.h"
#include "enum.h"

namespace
{
    static void FL_Hot_UP(int num1)
    {
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=90;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, num1);
        TestPWNStep(400);
    }

    static void FL_Hot_Dn(int num1)
    {
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, num1);
        TestPWNStep(400);
    }

    TEST(PWN,SI_TC_PWN_001)
    {
        TEST_PWNINIT();
        
       
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=95;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        
        TestPWNStep(100);

        
    }

    TEST(PWN,SI_TC_PWN_002)
    {
        TEST_PWNINIT();
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_003)
    {
        TEST_PWNINIT();
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_004)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_005)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);

        VeOUT_SP_PowerMode_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_006)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_007)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);

    }

    TEST(PWN,SI_TC_PWN_008)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
//        司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_HWA_FLStallSts_sig=1;
        TestPWNStep(32);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(1000);
        
        
    }

    TEST(PWN,SI_TC_PWN_009)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_010)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);


    }

    TEST(PWN,SI_TC_PWN_011)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_012)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_013)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_014)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //左前车窗 点动下降 下降- 司机侧开关
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_015)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //左前车窗 点动下降 下降- 司机侧开关
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_016)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //左前车窗 点动下降 下降- 司机侧开关
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_017)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //错误猜测，电压异常不输出
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_018)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_019)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_020)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //非 OFF 至 OFF 计时 120 秒超时
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_021)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //发生堵转?
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_HWA_FLStallSts_sig=1;
        TestPWNStep(32);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_022)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //进入电压保护
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_023)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //进入热保护状态？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_024)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //车窗同一动作持续超过 10 秒？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_025)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_026)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 2);
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(300);
        
        
    }

    TEST(PWN,SI_TC_PWN_027)
    {
        TEST_PWNINIT();
       
        // 左前车窗一 左前车窗一键上升 键上升-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //接收到司机侧电动窗开关一键上升 DSP_FLWndSwSts=0x3: Auto Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(100);

        
    }

    TEST(PWN,SI_TC_PWN_028)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_029)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_030)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_031)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_032)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_033)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_034)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_HWA_FLStallSts_sig=1;
        TestPWNStep(32);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_035)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_036)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_037)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_038)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_039)
    {
        TEST_PWNINIT();
        
       
        TestPWNStep(100);
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(6);
        VbINP_HWA_FLAntipinchEnable_flg=0;
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(210);
        
        
    }

    TEST(PWN,SI_TC_PWN_040)
    {
        TEST_PWNINIT();
        //在防夹区域内有阻力产生防夹？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_041)
    {
        TEST_PWNINIT();
        //车窗位置丢失？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(2);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_042)
    {
        TEST_PWNINIT();
        //左前车窗一键下降- 司机侧开关-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=160;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_043)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_044)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_045)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_046)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_047)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_048)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_VCCMFRDoorSts_flg=1;
        VbINP_HWA_FLAntipinchEnable_flg=0;
        TestPWNStep(160);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_049)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_HWA_FLStallSts_sig=1;
        TestPWNStep(32);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_050)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_051)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_052)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_053)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_054)
    {
        TEST_PWNINIT();
       
        TestPWNStep(100);
        
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(100);
        
        
        
    }

    TEST(PWN,SI_TC_PWN_055)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //在防夹区域内有阻力产生防夹？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_056)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //车窗位置丢失？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_FLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_FLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPFLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPFLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_057)
    {
        TEST_PWNINIT();
        //左后车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_058)
    {
        TEST_PWNINIT();
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_059)
    {
        TEST_PWNINIT();
        //前车窗 左前车窗 点动上升 上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_060)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_061)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_062)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_063)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_064)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_065)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_066)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_067)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_068)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_069)
    {
        TEST_PWNINIT();
        //同一车窗收到新的动作请求
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        VeINP_HWA_RLFeedBackRunSts_sig=1;
        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_070)
    {
        TEST_PWNINIT();
        //左后车窗 点动下降 下降- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动下降 DSP_RLWndSwSts=0x2: Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_071)
    {
        TEST_PWNINIT();
        //左后车窗 点动下降 下降- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_072)
    {
        TEST_PWNINIT();
        //左后车窗 点动下降 下降- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_073)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_074)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_075)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_076)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_077)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_078)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_079)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_080)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_081)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_082)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //接收到司机侧电动窗开关点动上升 DSP_RLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_HWA_RLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_083)
    {
        TEST_PWNINIT();
        //左后车窗一键上升 车窗一键上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //接收到司机侧电动窗开关一键上升 DSP_FLWndSwSts=0x3: Auto Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_084)
    {
        TEST_PWNINIT();
        //左后车窗一键上升 车窗一键上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_085)
    {
        TEST_PWNINIT();
        //左后车窗一键上升 车窗一键上升- 司机侧开关
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_086)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_087)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_088)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_089)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_090)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_091)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_092)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_093)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_094)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_095)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        VeINP_HWA_RLFeedBackRunSts_sig=2;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_096)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //在防夹区域内有阻力产生防夹？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_097)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //车窗位置丢失？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        //司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=1;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_098)
    {
        TEST_PWNINIT();
        //左前车窗一键下降- 司机侧开关-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_099)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_100)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_101)
    {
        TEST_PWNINIT();
        //错误猜测，电压异常不输出
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=70;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=4;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_102)
    {
        TEST_PWNINIT();
        //错误猜测，电源为OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_103)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;
        TestPWNStep(40);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_104)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_105)
    {
        TEST_PWNINIT();
        //发生堵转?
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_106)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_107)
    {
        TEST_PWNINIT();
        //进入热保护状态？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(6);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_108)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_109)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_110)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //同一车窗收到新的动作请求
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(10);
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        VeINP_HWA_FLFeedBackRunSts_sig=2;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_111)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //在防夹区域内有阻力产生防夹？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_112)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        //车窗位置丢失？？
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLAntipinchEnable_flg=0;

        //接收到司机侧电动窗开关一键下降 DSP_RLWndSwSts= 0x4: Auto Down Request
        TestPWNStep(6);
        VeINP_LIN_DSPRLWndSwSts_sig=2;
        TestPWNStep(10);
        VeINP_LIN_DSPRLWndSwSts_sig=0;
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_113)
    {
        TEST_PWNINIT();
        //左后车窗 左后车窗 点动上升 上升-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_114)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_115)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_116)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        TestPWNStep(10);
        VeOUT_SP_PowerMode_sig=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_117)
    {
        TEST_PWNINIT();
        //错误猜测：高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_118)
    {
        TEST_PWNINIT();
        //错误猜测：乘客车窗禁用功能使能
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_119)
    {
        TEST_PWNINIT();
        //发生堵转？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_120)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_121)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_122)
    {
        TEST_PWNINIT();
        //进入热保护状态???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_123)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_124)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞【怎么判断粘滞】???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_125)
    {
        TEST_PWNINIT();
       
        //同一车窗收到新的动作请求
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=2;
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(100);
        
        
    }

    TEST(PWN,SI_TC_PWN_126)
    {
        TEST_PWNINIT();
        //左后车窗 左后车窗 点动下降-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_127)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_128)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_129)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        TestPWNStep(10);
        VeOUT_SP_PowerMode_sig=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        
    }

    TEST(PWN,SI_TC_PWN_130)
    {
        TEST_PWNINIT();
        //错误猜测：高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_131)
    {
        TEST_PWNINIT();
        //错误猜测：乘客车窗禁用功能使能
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_132)
    {
        TEST_PWNINIT();
        //发生堵转？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_133)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_134)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_135)
    {
        TEST_PWNINIT();
        //进入热保护状态???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_136)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_137)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞【怎么判断粘滞】???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_138)
    {
        TEST_PWNINIT();
        //同一车窗收到新的动作请求
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VeINP_HWA_RLFeedBackRunSts_sig=2;
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 2);
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=0;
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(100);
        
    }

    TEST(PWN,SI_TC_PWN_139)
    {
        TEST_PWNINIT();
       
        //左后车窗一键上升-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_140)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_141)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_142)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        TestPWNStep(10);
        VeOUT_SP_PowerMode_sig=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        
    }

    TEST(PWN,SI_TC_PWN_143)
    {
        TEST_PWNINIT();
        //错误猜测：高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_144)
    {
        TEST_PWNINIT();
        //错误猜测：乘客车窗禁用功能使能
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_145)
    {
        TEST_PWNINIT();
        //发生堵转？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_RLWinPostion_sig=10;
        VuINP_HWA_RLWinPosMax_sig=4;
        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(32);
//        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 251);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_146)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngAutoUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_147)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngAutoUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_148)
    {
        TEST_PWNINIT();
        //进入热保护状态???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngAutoUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_149)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngAutoUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_150)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞【怎么判断粘滞】???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngAutoUp_flg=1;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_151)
    {
        TEST_PWNINIT();
        //同一车窗收到新的动作请求
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(6);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VbINP_HWA_RLPsngAutoUp_flg=0;
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        
    }

    TEST(PWN,SI_TC_PWN_152)
    {
        TEST_PWNINIT();
       
        //左后车窗一键下降- 左后本地开关-
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(10);
        
    }

    TEST(PWN,SI_TC_PWN_153)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_154)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_155)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;
        TestPWNStep(10);
        VeOUT_SP_PowerMode_sig=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_156)
    {
        TEST_PWNINIT();
        //错误猜测：高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_157)
    {
        TEST_PWNINIT();
        //错误猜测：乘客车窗禁用功能使能
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_158)
    {
        TEST_PWNINIT();
        //发生堵转？？
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        
    }

    TEST(PWN,SI_TC_PWN_159)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 计时 120 秒超时
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        TestPWNStep(10);
        TestPWNStep(12006);
        VeOUT_SP_PowerMode_sig=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_160)
    {
        TEST_PWNINIT();
        //进入电压保护
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_161)
    {
        TEST_PWNINIT();
        //进入热保护状态???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        TestPWNStep(10);
        TestPWNStep(12);
        VeINP_HWA_Voltage_100mV=200;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_162)
    {
        TEST_PWNINIT();
        //车窗同一动作持续超过 10 秒
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_163)
    {
        TEST_PWNINIT();
        //触发的开关发生粘滞【怎么判断粘滞】???
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManDn_flg=0;
        TestPWNStep(10);
        TestPWNStep(106);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_164)
    {
        TEST_PWNINIT();
        //同一车窗收到新的动作请求
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_CAN_ICMDisablePassengerWndReq_flg=0;
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //接收到左后本地开关点动上升且开关未发生粘滞？？
        TestPWNStep(6);
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VbINP_HWA_RLPsngManUp_flg=0;
        TestPWNStep(10);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(6);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VbINP_HWA_RLPsngAutoDn_flg=0;
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VbINP_HWA_RLPsngAutoDn_flg=1;
        TestPWNStep(21);
        
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_165)
    {
        TEST_PWNINIT();
        //6.1 车窗防夹初始化- 开关激活,手动按上升开关，直到车窗上升到顶部并发生堵转
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbINP_HWA_RLPsngManUp_flg=1;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VbINP_HWA_RLAntipinchEnable_flg=1;
////        司机侧电动窗开关点动上升 DSP_FLWndSwSts=0x1: Up Request
//        TestPWNStep(6);
//        VeINP_LIN_DSPFLWndSwSts_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 1);
        TestPWNStep(6);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
//        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 1);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        VeINP_HWA_RLStallSts_sig=1;
        TestPWNStep(30);
        //到顶部发生堵转？？
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
//        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        TestPWNStep(600);
        
    }

    TEST(PWN,SI_TC_PWN_166)
    {
        TEST_PWNINIT();
        
       
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeINP_HWA_RLFeedBackRunSts_sig=1;
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VuINP_HWA_RLWinPosMax_sig=720;
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        TestPWNStep(1000);
        
        
    }

    TEST(PWN,SI_TC_PWN_167)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_168)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
    }

    TEST(PWN,SI_TC_PWN_169)
    {
        TEST_PWNINIT();
        //错误猜测，OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
    }

    TEST(PWN,SI_TC_PWN_170)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_171)
    {
        TEST_PWNINIT();
        //6.1 车窗防夹初始化- 开关激活,手动按下降开关
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_172)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_173)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_174)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
    }

    TEST(PWN,SI_TC_PWN_175)
    {
        TEST_PWNINIT();
        //错误猜测，OFF
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
    }

    TEST(PWN,SI_TC_PWN_176)
    {
        TEST_PWNINIT();
        //非 OFF 至 OFF 120 秒内且前排车门未开启
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManDn_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_177)
    {
        TEST_PWNINIT();
        //6.2 车窗防夹使能???
        TestPWNStep(100);
        VbINP_HWA_RLAntipinchEnable_flg=1;
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;

        //到顶部发生堵转？？
        TestPWNStep(6);
        VbINP_HWA_RLPsngManUp_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_178)
    {
        TEST_PWNINIT();
        //6.3 乘客车窗禁用???
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //到顶部发生堵转？？
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULDisablePassengerWndSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_DisablePassengerWndStsToEE_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_179)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //到顶部发生堵转？？
        TestPWNStep(10);
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULDisablePassengerWndSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_180)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=0;

        //到顶部发生堵转？？
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULDisablePassengerWndSts_flg, 0);
    }

    TEST(PWN,SI_TC_PWN_181)
    {
        TEST_PWNINIT();
        TestPWNStep(100);
        VbINP_EPRM_DisablePassengerWndStsFromEE_flg=1;

        //到顶部发生堵转？？
        TestPWNStep(10);
        VbINP_CAN_ICMDisablePassengerWndReq_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VbOUT_PWN_ZCULDisablePassengerWndSts_flg, 1);
    }

    TEST(PWN,SI_TC_PWN_182)
    {
        TEST_PWNINIT();
        //7 功能定义- 车窗软停???

    }

    ///当前位置
    static void VentilateFLPostion(int num1) {
        VuINP_HWA_FLWinPosMax_sig = 600;//暂定
        VuINP_HWA_FLWinPostion_sig =num1* VuINP_HWA_FLWinPosMax_sig /100;
    }

    static void VentilateRLPostion(int num1) {
        VuINP_HWA_RLWinPosMax_sig = 500;//暂定
        VuINP_HWA_RLWinPostion_sig =num1* VuINP_HWA_RLWinPosMax_sig /100;
    }

//左前语音
    TEST(PWN,SI_TC_PWN_183)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
    }


//左前语音  达到期望位置
    TEST(PWN,SI_TC_PWN_184)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_185)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(20);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
        
        
    }

    TEST(PWN,SI_TC_PWN_186)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_187)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(20);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
    }

    TEST(PWN,SI_TC_PWN_188)
    {
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_189)
    {
        TEST_PWNINIT();
        //错误猜测，OFF档
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_190)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_191)
    {
        TEST_PWNINIT();
        //错误猜测，门未关
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_192)
    {
        TEST_PWNINIT();
        // 左后语音
       
        
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        
    }

    TEST(PWN,SI_TC_PWN_193)
    {
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        TestPWNStep(100);

        
        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_194)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(10);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
    }

    TEST(PWN,SI_TC_PWN_195)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
    }

    TEST(PWN,SI_TC_PWN_196)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
    }

    TEST(PWN,SI_TC_PWN_197)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_198)
    {
        TEST_PWNINIT();
        //错误猜测，OFF档
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_199)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_200)
    {
        TEST_PWNINIT();
        //错误猜测，门未关
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
    }


    TEST(PWN,SI_TC_PWN_201)
    {
        ///远程调节
        //int time = 0;
        TEST_PWNINIT();
        
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    ///远程控制车窗
    TEST(PWN,SI_TC_PWN_202)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_203)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_204)
    {
        TEST_PWNINIT();
        //错误猜测。非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_205)
    {
        TEST_PWNINIT();
        //错误猜测。非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_206)
    {
        TEST_PWNINIT();
        //错误猜测。非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_207)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_PDU_ZCULSystemPowerSource_sig = 3;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=5;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_208)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=250;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_209)
    {
        TEST_PWNINIT();
        //错误猜测，门未关
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPFLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_210)
    {
        TEST_PWNINIT();
        // 左后远程
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_211)
    {
        TEST_PWNINIT();
       
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_212)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_213)
    {
        TEST_PWNINIT();
        //错误猜测，非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_214)
    {
        TEST_PWNINIT();
        //错误猜测，非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_215)
    {
        TEST_PWNINIT();
        //错误猜测，非OFF
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_216)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);
        GTEST_SKIP();
        ////VeOUT_CMS_ZCULCarMode_sig=1;

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=5;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_217)
    {
        TEST_PWNINIT();
        //错误猜测，高压
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_218)
    {
        TEST_PWNINIT();
        //错误猜测，门未关
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_TCPRLWindowsControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_219)
    {
        //10.1 蓝牙升降车窗- 左前/
        //
        //车窗防夹完成自学习？？
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_220)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_221)
    {
        TEST_PWNINIT();
        ///没有运行到期望位置
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
        
        
    }

    TEST(PWN,SI_TC_PWN_222)
    {
        TEST_PWNINIT();
        ///非OFF档
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_223)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_224)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_225)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=5;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_226)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(30);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_227)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbINP_HWA_FLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=5;
        TestPWNStep(6);
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_FLOutMovTypeState_enum == 1 || VmOUT_PWN_FLOutMovTypeState_enum == 3)
            { VeINP_HWA_FLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_FLOutMovTypeState_enum == 2 || VmOUT_PWN_FLOutMovTypeState_enum == 4)
            {VeINP_HWA_FLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_FLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateFLPostion(50);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_228)
    {
        TEST_PWNINIT();
        // 左后远程
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(30);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        
        
    }

    TEST(PWN,SI_TC_PWN_229){
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
    }

    TEST(PWN,SI_TC_PWN_230)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=6;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(60);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_231)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_232)
    {
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TEST_PWNINIT();
        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_233)
    {
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TEST_PWNINIT();
        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=3;
        ////VbOUT_PDU_PowerModeValid_flg=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_234)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=1;
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=5;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
    }

    TEST(PWN,SI_TC_PWN_235)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }

    TEST(PWN,SI_TC_PWN_236)
    {
        TEST_PWNINIT();
        VentilateFLPostion(40);
        VentilateRLPostion(40);

        TestPWNStep(100);

        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        VbINP_HWA_RLAntipinchEnable_flg=1;

        TestPWNStep(6);
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;
        for(int time = 0;time<30;time++) {
            TestPWNStep(1);
            if (VmOUT_PWN_RLOutMovTypeState_enum == 1 || VmOUT_PWN_RLOutMovTypeState_enum == 3)
            { VeINP_HWA_RLFeedBackRunSts_sig = 1; }
            else if(VmOUT_PWN_RLOutMovTypeState_enum == 2 || VmOUT_PWN_RLOutMovTypeState_enum == 4)
            {VeINP_HWA_RLFeedBackRunSts_sig = 2;}
            if(VeINP_HWA_RLFeedBackRunSts_sig!=0)
                break;
        }
        TestPWNStep(6);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        TestPWNStep(500);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        TestPWNStep(30);
        VentilateRLPostion(50);
        VeINP_HWA_RLFeedBackRunSts_sig=0;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
    }
//
    TEST(PWN,SI_TC_PWN_237)
    {
        TEST_PWNINIT();
        //11.1 设防升车窗&车窗 车窗 透气设置
        // (设防升窗)
       
        TestPWNStep(100);
        
        VmOUT_ALM_PWNReq_enum=0;
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VeINP_CAN_ICMArmedCloseWndReq_sig=1;//锁车关窗设置请求信号有效,全关
        TestPWNStep(10);
        VmOUT_ALM_PWNReq_enum=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(40);
        
        
    }
//
    TEST(PWN,SI_TC_PWN_238)
    {
        TEST_PWNINIT();
        //11.1 设防升车窗&车窗 车窗 透气设置
        // (设防升窗)
       
        TestPWNStep(100);
        
        VmOUT_ALM_PWNReq_enum=0;
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=40;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VeINP_CAN_ICMArmedCloseWndReq_sig=1;//锁车关窗设置请求信号有效,全关
        TestPWNStep(10);
        VmOUT_ALM_PWNReq_enum=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_239)
    {
        TEST_PWNINIT();
        //11.1 设防升车窗&车窗 车窗 透气设置
        // (设防升窗)
       
        TestPWNStep(100);
        
        VmOUT_ALM_PWNReq_enum=0;
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VeINP_CAN_ICMArmedCloseWndReq_sig=1;//锁车关窗设置请求信号有效,全关
        TestPWNStep(10);
        VmOUT_ALM_PWNReq_enum=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    TEST(PWN,SI_TC_PWN_240)
    {
        TEST_PWNINIT();
        //11.1 设防升车窗&车窗 车窗 透气设置
        // (设防升窗)
       
        TestPWNStep(100);
        
        VmOUT_ALM_PWNReq_enum=0;
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        TestPWNStep(10);
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        VeINP_CAN_ICMArmedCloseWndReq_sig=1;//锁车关窗设置请求信号有效,全关
        TestPWNStep(10);
        VmOUT_ALM_PWNReq_enum=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
    }

    TEST(PWN,SI_TC_PWN_241)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
    }

//远程模式
    TEST(PWN,SI_TC_PWN_242)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
    }

    //！90~160V
    TEST(PWN,SI_TC_PWN_243)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    //！90~160V
    TEST(PWN,SI_TC_PWN_244)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=2;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=200;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

    //！左前门开
    TEST(PWN,SI_TC_PWN_245)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=100;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

//右前门开
    TEST(PWN,SI_TC_PWN_246)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=100;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }


//右前门开
    TEST(PWN,SI_TC_PWN_247)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=100;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }


//右前门开
    TEST(PWN,SI_TC_PWN_248)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=100;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=1;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }


//右前门开
    TEST(PWN,SI_TC_PWN_249)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=140;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=1;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
    }

//后尾门开
    TEST(PWN,SI_TC_PWN_250)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }

    ///升窗后 电源不为OFF
    TEST(PWN,SI_TC_PWN_251)
    {
        TEST_PWNINIT();
        
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(40);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeOUT_SP_PowerMode_sig=1;
        TestPWNStep(10);
//        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
        
    }

    ///下雨关窗 ！电源模式为 OFF 或电源处于远程模式
    TEST(PWN,SI_TC_PWN_252)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(10);
        VeINP_HWA_FLFeedBackRunSts_sig=0;
        VeOUT_SP_PowerMode_sig=2;
        TestPWNStep(10);
//        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }


    ///升窗后 门开
    TEST(PWN,SI_TC_PWN_253)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(40);
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        TestPWNStep(10);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }
    TEST(PWN,SI_TC_PWN_254)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(20);
        VbOUT_SP_ZCULRLDoorSts_flg=1;
        TestPWNStep(20);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
        
    }

    TEST(PWN,SI_TC_PWN_255)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(10);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(40);
        VbINP_CAN_VCCMFRDoorSts_flg=1;
        TestPWNStep(20);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        
    }

    TEST(PWN,SI_TC_PWN_256)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(40);
        VbINP_CAN_VCCMRRDoorSts_flg=1;
        TestPWNStep(20);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        TestPWNStep(100);
        
    }

    TEST(PWN,SI_TC_PWN_257)
    {
        TEST_PWNINIT();
        //12.1 下雨关窗设置
       
        TestPWNStep(100);
        
        VeOUT_SP_PowerMode_sig=0;
        ////VbOUT_PDU_PowerModeValid_flg=0;
        VeINP_HWA_Voltage_100mV=120;
        VbOUT_SP_ZCULFLDoorSts_flg=0;
        VbOUT_SP_ZCULRLDoorSts_flg=0;
        VbINP_CAN_VCCMFRDoorSts_flg=0;
        VbINP_CAN_VCCMRRDoorSts_flg=0;
        VbINP_CAN_VCCMTrunkDoorSts_flg=0;
        VbINP_HWA_FLAntipinchEnable_flg=1;
        VbINP_CAN_ICMRainAutoClosedWndReq_flg=1;
        VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg=1;
        VeINP_HWA_FLFeedBackRunSts_sig=1;
        VuINP_HWA_FLWinPostion_sig=10;
        VuINP_HWA_FLWinPosMax_sig=4;
        TestPWNStep(10);
        TestPWNStep(6);
        VeINP_CAN_RLSWinCloseCmd_sig=1;
        TestPWNStep(40);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        TestPWNStep(40);
        VbINP_CAN_VCCMTrunkDoorSts_flg=1;
        TestPWNStep(20);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        
    }
}
