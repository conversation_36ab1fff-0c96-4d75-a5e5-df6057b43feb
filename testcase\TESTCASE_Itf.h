//
// Created by allen on 2021/3/20.
//

#ifndef CX62BTEST_TESTCASE_ITF_H
#define CX62BTEST_TESTCASE_ITF_H

#include "TEST_API.h"
#include "RTE.h"

//#define TEST_INIT BDC_EP36_initialize();

#define TESTSCU_INIT   SEAT_Model_initialize();
//**********************   Inputs  **************************//
#define IGNSW_ON VbINP_HWA_IGN_flg = true
#define IGNSW_OFF VbINP_HWA_IGN_flg = false
#define ACCSW_ON VbINP_HWA_ACC_flg = true
#define ACCSW_OFF VbINP_HWA_ACC_flg = false
#define ParkLightSW_ON VbINP_HWA_ParkLightSw_flg = true
#define ParkLightSW_OFF VbINP_HWA_ParkLightSw_flg = false
#define LowBeamSW_ON VbINP_HWA_LowBeamSw_flg = true
#define LowBeamSW_OFF VbINP_HWA_LowBeamSw_flg = false
#define HIGHBEAMSW_ON VbINP_HWA_HighBeam_flg = true
#define HIGHBEAMSW_OFF VbINP_HWA_HighBeam_flg = false
#define FLASHSW_ON VbINP_HWA_Flash_flg = true
#define FLASHSW_OFF VbINP_HWA_Flash_flg = false





#define HAZARDSW_PRESSED VbINP_HWA_Hazard_flg = true
#define HAZARDSW_RELEASED VbINP_HWA_Hazard_flg = false
#define BRAKE_ON_CAN VbINP_CAN_Brake_flg = true
#define BRAKE_OFF_CAN VbINP_CAN_Brake_flg = false
#define L_TURN_SW_ON VbINP_HWA_LTurn_flg = true
#define L_TURN_SW_OFF VbINP_HWA_LTurn_flg = false
#define R_TURN_SW_ON VbINP_HWA_RTurn_flg = true
#define R_TURN_SW_OFF VbINP_HWA_RTurn_flg = false
#define F_WIPER_PARK_POS_ON VbINP_HWA_FWiperPark_flg = true
#define F_WIPER_PARK_POS_OFF VbINP_HWA_FWiperPark_flg = false
#define F_WIPER_LOW_SPEED_ON VbINP_HWA_FWiperLow_flg = true
#define F_WIPER_LOW_SPEED_OFF VbINP_HWA_FWiperLow_flg = false
#define F_WIPER_HIGH_SPEED_ON VbINP_HWA_FWiperHigh_flg = true
#define F_WIPER_HIGH_SPEED_OFF VbINP_HWA_FWiperHigh_flg = false
#define RKE_LOCK_ON VbINP_HWA_RKELockValid_flg = true
#define RKE_LOCK_OFF VbINP_HWA_RKELockValid_flg = false
#define FL_LOCK_ON VbINP_HWA_FLLockSts_flg = true
#define FL_LOCK_OFF VbINP_HWA_FLLockSts_flg = false

//***********************  Outputs **************************//

#define LowBeam_STS VbOUT_LIT_LowBeam_flg
#define FParkLightHWA_STS VbOUT_HWA_FParkLight_flg
#define FParkLightCAN_STS VbOUT_CAN_FParkLight_flg
#define RParkLightHWA_STS VbOUT_HWA_RParkLight_flg
#define RParkLightCAN_STS VbOUT_CAN_RParkLight_flg
#define LightBackHWA_STS VbOUT_HWA_LightBack_flg
#define LightBackSSB_STS VbOUT_SSB_LightBack_flg
#define HighBeam_STS VbOUT_LIT_HighBeam_flg



#define LDRL_STS VbOUT_LIT_LeftDRL_flg
#define RDRL_STS VbOUT_LIT_RightDRL_flg
#define BRAKE_LIGHT_STS VbOUT_LIT_BrakeLight_flg
#define HIGH_BEAM_ASSIST_STS VbOUT_LIT_HighBeamAsst_flg



//*******************      CAN signals ****************************//
#define ENGINE_STS VbINP_CAN_EngineStsReady_flg
#define VEH_SPEED_KMH VeINP_CAN_VehSpeed_kmh
#define DVD_SET_HIGH_BEAM_ASSIST VeINP_CAN_HighBeamAssist_sig


#endif //CX62BTEST_TESTCASE_ITF_H
