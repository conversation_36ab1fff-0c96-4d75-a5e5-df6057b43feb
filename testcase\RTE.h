#ifdef __cplusplus
extern "C" {
#endif
#include "rtwtypes.h"
#include "Std_Types.h"
#include "Compiler.h"
#include "Rte_Type.h"

#ifndef SEAT_MODELTEST_RTENEW_H
#define SEAT_MODELTEST_RTENEW_H

extern UInt8 VmOUT_PWN_FLOutMovTypeState_enum;
extern UInt8 VmOUT_PWN_RLOutMovTypeState_enum;
extern UInt8 VmOUT_PWN_FLOutMotorState_enum;
extern UInt8 VmOUT_PWN_RLOutMotorState_enum;
extern UInt8 VmOUT_ALM_AlarmState_enum;
extern UInt8 VmOUT_ALM_PWNReq_enum;
extern UInt8 VeOUT_PWN_ZCULFLWindowStatus_sig;
extern UInt8 VeOUT_PWN_ZCULRLWindowStatus_sig;
extern Boolean VbOUT_PWN_ZCULDisablePassengerWndSts_flg;
extern UInt8 VmOUT_PWN_FLOutMovTypeState_enum;
extern UInt8 VmOUT_PWN_RLOutMovTypeState_enum;
extern UInt8 VmOUT_PWN_FLOutMotorState_enum;
extern UInt8 VmOUT_PWN_RLOutMotorState_enum;
extern Boolean VbOUT_PWN_FLDrvBultStuck_flg;
extern Boolean VbOUT_PWN_FRDrvBultStuck_flg;
extern Boolean VbOUT_PWN_RLDrvBultStuck_flg;
extern Boolean VbOUT_PWN_RRDrvBultStuck_flg;
extern Boolean VbOUT_PWN_RLPsgBultStuck_flg;
extern Boolean VbOUT_PWN_ZCULFLWinInitializedSts_flg;
extern Boolean VbOUT_PWN_ZCULRLWinInitializedSts_flg;
extern Boolean VbOUT_PWN_DisablePassengerWndStsToEE_flg;
extern UInt8 VeOUT_PWN_PWNStatusResponse2ICM_sig;
extern UInt8 VeOUT_PWN_PWNStatusResponse2TCP_sig;
extern UInt8 VeOUT_PWN_PWNStatusResponse2DKM_sig;
extern Boolean VbOUT_PWN_ZCULRainAutoClosedWndSts_flg;
extern Boolean VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg;
extern UInt8 VeOUT_PWN_ZCULFRWindowsControl_sig;
extern UInt8 VeOUT_PWN_ZCULRRWindowsControl_sig;
extern UInt8 VeOUT_PWN_ZCULFRWINCtrlCmd_sig;
extern UInt8 VeOUT_PWN_ZCULRRWINCtrlCmd_sig;
extern Boolean VbOUT_PWN_ALMWinUpError_flg;
extern Boolean VbOUT_PWN_AllWinCloseStsFb_flg;
extern UInt8 VeOUT_PWN_FLWinSrcHst_Array[10];
extern UInt8 VeOUT_PWN_RLWinSrcHst_Array[10];
extern Boolean VbOUT_PWN_FLWinOhpmode_flg;
extern Boolean VbOUT_PWN_RLWinOhpmode_flg;
extern Boolean VbOUT_PWN_FLForceWinCloseReq_flg;
extern Boolean VbOUT_PWN_RLForceWinCloseReq_flg;
extern UInt8 VeOUT_PWN_ZCULArmedCloseWndSts_sig;
extern UInt8 VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig;
extern Boolean VbOUT_PWN_ZCULWindowSts_flg;
extern UInt8 VeOUT_PWN_ZCULSourceCMDFR_sig;
extern UInt8 VeOUT_PWN_ZCULSourceCMDRR_sig;
extern UInt32 VnOUT_PWN_PWNFailReason2ICM_sig;
extern UInt32 VnOUT_PWN_PWNFailReason2TCP_sig;
extern UInt32 VnOUT_PWN_PWNFailReason2DKM_sig;
extern UInt8 VeOUT_PWN_ZCULSourcebackFL_sig;
extern UInt8 VeOUT_PWN_ZCULSourcebackRL_sig;
extern Boolean VbOUT_PWN_SleepPermit_flg;
extern UInt8 VeINP_LIN_DSPFLWndSwSts_sig;
extern UInt8 VeINP_LIN_DSPFRWndSwSts_sig;
extern UInt8 VeINP_LIN_DSPRLWndSwSts_sig;
extern UInt8 VeINP_LIN_DSPRRWndSwSts_sig;
extern Boolean VbINP_HWA_RLPsngManUp_flg;
extern Boolean VbINP_HWA_RLPsngManDn_flg;
extern Boolean VbINP_HWA_RLPsngAutoUp_flg;
extern Boolean VbINP_HWA_RLPsngAutoDn_flg;
extern Boolean VbOUT_SP_ZCULFLDoorSts_flg;
extern Boolean VbOUT_SP_ZCULRLDoorSts_flg;
extern UInt8 VeINP_HWA_Voltage_100mV;
extern UInt8 VeINP_HWA_FLFeedBackRunSts_sig;
extern UInt8 VeINP_HWA_RLFeedBackRunSts_sig;
extern UInt8 VeINP_HWA_FLStallSts_sig;
extern UInt8 VeINP_HWA_RLStallSts_sig;
extern UInt16 VuINP_HWA_FLWinPostion_sig;
extern UInt16 VuINP_HWA_RLWinPostion_sig;
extern Boolean VbINP_HWA_FLAntipinchEnable_flg;
extern Boolean VbINP_HWA_RLAntipinchEnable_flg;
extern UInt16 VuINP_HWA_FLWinPosMax_sig;
extern UInt16 VuINP_HWA_RLWinPosMax_sig;
extern Boolean VbINP_CAN_ICMDisablePassengerWndReq_flg;
extern Boolean VbINP_EPRM_DisablePassengerWndStsFromEE_flg;
extern Boolean VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg;
extern Boolean VbINP_CAN_VCCMFRDoorSts_flg;
extern Boolean VbINP_CAN_VCCMRRDoorSts_flg;
extern Boolean VbINP_CAN_VCCMTrunkDoorSts_flg;
extern UInt8 VeINP_CAN_ICMFLWindowsVoiControl_sig;
extern UInt8 VeINP_CAN_ICMRLWindowsVoiControl_sig;
extern UInt8 VeINP_CAN_ICMFRWindowsVoiControl_sig;
extern UInt8 VeINP_CAN_ICMRRWindowsVoiControl_sig;
extern UInt8 VeINP_CAN_TCPFLWindowsControl_sig;
extern UInt8 VeINP_CAN_TCPRLWindowsControl_sig;
extern UInt8 VeINP_CAN_TCPFRWindowsControl_sig;
extern UInt8 VeINP_CAN_TCPRRWindowsControl_sig;
extern UInt8 VeINP_CAN_DKMVCCMFLWindowsControl_sig;
extern UInt8 VeINP_CAN_DKMVCCMRLWindowsControl_sig;
extern UInt8 VeINP_CAN_DKMVCCMFRWindowsControl_sig;
extern UInt8 VeINP_CAN_DKMVCCMRRWindowsControl_sig;
extern UInt8 VeINP_CAN_ICMArmedCloseWndReq_sig;
extern UInt8 VeINP_CAN_RLSWinCloseCmd_sig;
extern Boolean VbINP_CAN_ICMRainAutoClosedWndReq_flg;
extern UInt8 VeOUT_PDU_ZCULSystemPowerSource_sig;
extern Boolean VbINP_CAN_VCCMFRWinInitializedSts_flg;
extern Boolean VbINP_CAN_VCCMRRWinInitializedSts_flg;
extern UInt8 VeINP_CAN_VCCMFRWindowStatus_sig;
extern UInt8 VeINP_CAN_VCCMRRWindowStatus_sig;
extern UInt8 VeINP_CAN_VCCMFRWindowsMovSt_sig;
extern UInt8 VeINP_CAN_VCCMRRWindowsMovSt_sig;
extern Boolean VbINP_CAN_VCCMFRAntipinchSts_flg;
extern Boolean VbINP_CAN_VCCMRRAntipinchSts_flg;
extern UInt8 VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig;
extern UInt8 VeINP_CAN_VCCMSourcebackFR_sig;
extern UInt8 VeINP_CAN_VCCMSourcebackRR_sig;
extern UInt8 VeINP_CAN_VCCMRunErrCauseFR_sig;
extern UInt8 VeINP_CAN_VCCMRunErrCauseRR_sig;
extern UInt8 VmOUT_ALM_AlarmState_enum;
extern UInt8 VmOUT_ALM_PWNReq_enum;
extern UInt8 VeINP_CAN_VCU1NActualGear_sig;
extern UInt8 VeINP_HWA_FLWindowErrSource_sig;
extern UInt8 VeINP_HWA_RLWindowErrSource_sig;
extern UInt8 VeINP_CAN_ICMOTASts_sig;
extern UInt8 VeOUT_SP_PowerMode_sig;
extern UInt8 VeINP_HWA_SleepCommand_sig;
extern UInt8 VeOUT_CMS_ZCULCarMode_sig;
extern Boolean VbINP_BSW_EEReady_flg;
extern Boolean VbINP_HWA_FLPsngManUp_flg;
extern Boolean VbINP_HWA_FLPsngManDn_flg;
extern Boolean VbINP_HWA_FLPsngAutoUp_flg;
extern Boolean VbINP_HWA_FLPsngAutoDn_flg;
extern UInt8 VeINP_CAN_ICMWashModeSwSts_sig;
extern Boolean VbINP_CAN_ICMCampingModeSwSts_flg;
extern Boolean VbINP_CAN_ICMOffVehPowerKeepSwSts_flg;
extern Boolean VbINP_CAN_PEPSRKElockSts_flg;
extern Boolean VbINP_CAN_PEPSRKEunlockSts_flg;
extern Boolean VbOUT_PDU_PowerModeValid_flg;
extern UInt8 VeOUT_ALM_ZCULAntiThelfSts_sig;
extern Boolean VbINP_CAN_ICMInCarCampingSwSts_flg;
extern Boolean VbINP_CFG_LeRiRudder_flg;
extern Boolean VbINP_CAN_DKMWindowClose_flg;
extern Boolean VbINP_CAN_DKMWindowOpen_flg;
extern Boolean VbOUT_CMS_NAPModeSts_flg;

void TEST_RESET(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(void);

extern Std_ReturnType Rte_Write_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(void);

extern Std_ReturnType Rte_Write_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(void);

extern Std_ReturnType Rte_Write_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(void);

extern Std_ReturnType Rte_Write_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_FLWinSrcHst_Array_VeOUT_PWN_FLWinSrcHst_Array(UInt8 * u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_FLWinSrcHst_Array_VeOUT_PWN_FLWinSrcHst_Array(UInt8 * u);

extern Std_ReturnType Rte_Write_VeOUT_PWN_RLWinSrcHst_Array_VeOUT_PWN_RLWinSrcHst_Array(UInt8 * u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_RLWinSrcHst_Array_VeOUT_PWN_RLWinSrcHst_Array(UInt8 * u);

extern Std_ReturnType Rte_Write_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(void);

extern Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(UInt32 u);

extern Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(UInt32* u);

extern Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(void);

extern Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(UInt32 u);

extern Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(UInt32* u);

extern Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(void);

extern Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(UInt32 u);

extern Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(UInt32* u);

extern Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(void);

extern Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(UInt8* u);

extern Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(void);

extern Std_ReturnType Rte_Write_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(Boolean* u);

extern Std_ReturnType Rte_Invalidate_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(void);

extern Std_ReturnType Rte_Write_VeINP_LIN_DSPFLWndSwSts_sig_VeINP_LIN_DSPFLWndSwSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_LIN_DSPFLWndSwSts_sig_VeINP_LIN_DSPFLWndSwSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_LIN_DSPFRWndSwSts_sig_VeINP_LIN_DSPFRWndSwSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_LIN_DSPFRWndSwSts_sig_VeINP_LIN_DSPFRWndSwSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_LIN_DSPRLWndSwSts_sig_VeINP_LIN_DSPRLWndSwSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_LIN_DSPRLWndSwSts_sig_VeINP_LIN_DSPRLWndSwSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_LIN_DSPRRWndSwSts_sig_VeINP_LIN_DSPRRWndSwSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_LIN_DSPRRWndSwSts_sig_VeINP_LIN_DSPRRWndSwSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_RLPsngManUp_flg_VbINP_HWA_RLPsngManUp_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_RLPsngManUp_flg_VbINP_HWA_RLPsngManUp_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_RLPsngManDn_flg_VbINP_HWA_RLPsngManDn_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_RLPsngManDn_flg_VbINP_HWA_RLPsngManDn_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_RLPsngAutoUp_flg_VbINP_HWA_RLPsngAutoUp_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_RLPsngAutoUp_flg_VbINP_HWA_RLPsngAutoUp_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_RLPsngAutoDn_flg_VbINP_HWA_RLPsngAutoDn_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_RLPsngAutoDn_flg_VbINP_HWA_RLPsngAutoDn_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbOUT_SP_ZCULFLDoorSts_flg_VbOUT_SP_ZCULFLDoorSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_SP_ZCULFLDoorSts_flg_VbOUT_SP_ZCULFLDoorSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbOUT_SP_ZCULRLDoorSts_flg_VbOUT_SP_ZCULRLDoorSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_SP_ZCULRLDoorSts_flg_VbOUT_SP_ZCULRLDoorSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_Voltage_100mV_VeINP_HWA_Voltage_100mV(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_Voltage_100mV_VeINP_HWA_Voltage_100mV(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_FLFeedBackRunSts_sig_VeINP_HWA_FLFeedBackRunSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_FLFeedBackRunSts_sig_VeINP_HWA_FLFeedBackRunSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_RLFeedBackRunSts_sig_VeINP_HWA_RLFeedBackRunSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_RLFeedBackRunSts_sig_VeINP_HWA_RLFeedBackRunSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_FLStallSts_sig_VeINP_HWA_FLStallSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_FLStallSts_sig_VeINP_HWA_FLStallSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_RLStallSts_sig_VeINP_HWA_RLStallSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_RLStallSts_sig_VeINP_HWA_RLStallSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VuINP_HWA_FLWinPostion_sig_VuINP_HWA_FLWinPostion_sig(UInt16 u);

extern Std_ReturnType Rte_Read_VuINP_HWA_FLWinPostion_sig_VuINP_HWA_FLWinPostion_sig(UInt16* u);

extern Std_ReturnType Rte_Write_VuINP_HWA_RLWinPostion_sig_VuINP_HWA_RLWinPostion_sig(UInt16 u);

extern Std_ReturnType Rte_Read_VuINP_HWA_RLWinPostion_sig_VuINP_HWA_RLWinPostion_sig(UInt16* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_FLAntipinchEnable_flg_VbINP_HWA_FLAntipinchEnable_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_FLAntipinchEnable_flg_VbINP_HWA_FLAntipinchEnable_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_RLAntipinchEnable_flg_VbINP_HWA_RLAntipinchEnable_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_RLAntipinchEnable_flg_VbINP_HWA_RLAntipinchEnable_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VuINP_HWA_FLWinPosMax_sig_VuINP_HWA_FLWinPosMax_sig(UInt16 u);

extern Std_ReturnType Rte_Read_VuINP_HWA_FLWinPosMax_sig_VuINP_HWA_FLWinPosMax_sig(UInt16* u);

extern Std_ReturnType Rte_Write_VuINP_HWA_RLWinPosMax_sig_VuINP_HWA_RLWinPosMax_sig(UInt16 u);

extern Std_ReturnType Rte_Read_VuINP_HWA_RLWinPosMax_sig_VuINP_HWA_RLWinPosMax_sig(UInt16* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_ICMDisablePassengerWndReq_flg_VbINP_CAN_ICMDisablePassengerWndReq_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_ICMDisablePassengerWndReq_flg_VbINP_CAN_ICMDisablePassengerWndReq_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_EPRM_DisablePassengerWndStsFromEE_flg_VbINP_EPRM_DisablePassengerWndStsFromEE_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_EPRM_DisablePassengerWndStsFromEE_flg_VbINP_EPRM_DisablePassengerWndStsFromEE_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRDoorSts_flg_VbINP_CAN_VCCMFRDoorSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRDoorSts_flg_VbINP_CAN_VCCMFRDoorSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRDoorSts_flg_VbINP_CAN_VCCMRRDoorSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRDoorSts_flg_VbINP_CAN_VCCMRRDoorSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMTrunkDoorSts_flg_VbINP_CAN_VCCMTrunkDoorSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMTrunkDoorSts_flg_VbINP_CAN_VCCMTrunkDoorSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMFLWindowsVoiControl_sig_VeINP_CAN_ICMFLWindowsVoiControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMFLWindowsVoiControl_sig_VeINP_CAN_ICMFLWindowsVoiControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMRLWindowsVoiControl_sig_VeINP_CAN_ICMRLWindowsVoiControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMRLWindowsVoiControl_sig_VeINP_CAN_ICMRLWindowsVoiControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMFRWindowsVoiControl_sig_VeINP_CAN_ICMFRWindowsVoiControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMFRWindowsVoiControl_sig_VeINP_CAN_ICMFRWindowsVoiControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMRRWindowsVoiControl_sig_VeINP_CAN_ICMRRWindowsVoiControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMRRWindowsVoiControl_sig_VeINP_CAN_ICMRRWindowsVoiControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_TCPFLWindowsControl_sig_VeINP_CAN_TCPFLWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_TCPFLWindowsControl_sig_VeINP_CAN_TCPFLWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_TCPRLWindowsControl_sig_VeINP_CAN_TCPRLWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_TCPRLWindowsControl_sig_VeINP_CAN_TCPRLWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_TCPFRWindowsControl_sig_VeINP_CAN_TCPFRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_TCPFRWindowsControl_sig_VeINP_CAN_TCPFRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_TCPRRWindowsControl_sig_VeINP_CAN_TCPRRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_TCPRRWindowsControl_sig_VeINP_CAN_TCPRRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMFLWindowsControl_sig_VeINP_CAN_DKMVCCMFLWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMFLWindowsControl_sig_VeINP_CAN_DKMVCCMFLWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMRLWindowsControl_sig_VeINP_CAN_DKMVCCMRLWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMRLWindowsControl_sig_VeINP_CAN_DKMVCCMRLWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMFRWindowsControl_sig_VeINP_CAN_DKMVCCMFRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMFRWindowsControl_sig_VeINP_CAN_DKMVCCMFRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMRRWindowsControl_sig_VeINP_CAN_DKMVCCMRRWindowsControl_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMRRWindowsControl_sig_VeINP_CAN_DKMVCCMRRWindowsControl_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMArmedCloseWndReq_sig_VeINP_CAN_ICMArmedCloseWndReq_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMArmedCloseWndReq_sig_VeINP_CAN_ICMArmedCloseWndReq_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_RLSWinCloseCmd_sig_VeINP_CAN_RLSWinCloseCmd_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_RLSWinCloseCmd_sig_VeINP_CAN_RLSWinCloseCmd_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_ICMRainAutoClosedWndReq_flg_VbINP_CAN_ICMRainAutoClosedWndReq_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_ICMRainAutoClosedWndReq_flg_VbINP_CAN_ICMRainAutoClosedWndReq_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeOUT_PDU_ZCULSystemPowerSource_sig_VeOUT_PDU_ZCULSystemPowerSource_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_PDU_ZCULSystemPowerSource_sig_VeOUT_PDU_ZCULSystemPowerSource_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRWinInitializedSts_flg_VbINP_CAN_VCCMFRWinInitializedSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRWinInitializedSts_flg_VbINP_CAN_VCCMFRWinInitializedSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRWinInitializedSts_flg_VbINP_CAN_VCCMRRWinInitializedSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRWinInitializedSts_flg_VbINP_CAN_VCCMRRWinInitializedSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMFRWindowStatus_sig_VeINP_CAN_VCCMFRWindowStatus_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMFRWindowStatus_sig_VeINP_CAN_VCCMFRWindowStatus_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMRRWindowStatus_sig_VeINP_CAN_VCCMRRWindowStatus_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMRRWindowStatus_sig_VeINP_CAN_VCCMRRWindowStatus_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMFRWindowsMovSt_sig_VeINP_CAN_VCCMFRWindowsMovSt_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMFRWindowsMovSt_sig_VeINP_CAN_VCCMFRWindowsMovSt_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMRRWindowsMovSt_sig_VeINP_CAN_VCCMRRWindowsMovSt_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMRRWindowsMovSt_sig_VeINP_CAN_VCCMRRWindowsMovSt_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRAntipinchSts_flg_VbINP_CAN_VCCMFRAntipinchSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRAntipinchSts_flg_VbINP_CAN_VCCMFRAntipinchSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRAntipinchSts_flg_VbINP_CAN_VCCMRRAntipinchSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRAntipinchSts_flg_VbINP_CAN_VCCMRRAntipinchSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMSourcebackFR_sig_VeINP_CAN_VCCMSourcebackFR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMSourcebackFR_sig_VeINP_CAN_VCCMSourcebackFR_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMSourcebackRR_sig_VeINP_CAN_VCCMSourcebackRR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMSourcebackRR_sig_VeINP_CAN_VCCMSourcebackRR_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMRunErrCauseFR_sig_VeINP_CAN_VCCMRunErrCauseFR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMRunErrCauseFR_sig_VeINP_CAN_VCCMRunErrCauseFR_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCCMRunErrCauseRR_sig_VeINP_CAN_VCCMRunErrCauseRR_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCCMRunErrCauseRR_sig_VeINP_CAN_VCCMRunErrCauseRR_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VmOUT_ALM_AlarmState_enum_VmOUT_ALM_AlarmState_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_ALM_AlarmState_enum_VmOUT_ALM_AlarmState_enum(UInt8* u);

extern Std_ReturnType Rte_Write_VmOUT_ALM_PWNReq_enum_VmOUT_ALM_PWNReq_enum(UInt8 u);

extern Std_ReturnType Rte_Read_VmOUT_ALM_PWNReq_enum_VmOUT_ALM_PWNReq_enum(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_VCU1NActualGear_sig_VeINP_CAN_VCU1NActualGear_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_VCU1NActualGear_sig_VeINP_CAN_VCU1NActualGear_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_FLWindowErrSource_sig_VeINP_HWA_FLWindowErrSource_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_FLWindowErrSource_sig_VeINP_HWA_FLWindowErrSource_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_RLWindowErrSource_sig_VeINP_HWA_RLWindowErrSource_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_RLWindowErrSource_sig_VeINP_HWA_RLWindowErrSource_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMOTASts_sig_VeINP_CAN_ICMOTASts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMOTASts_sig_VeINP_CAN_ICMOTASts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeOUT_SP_PowerMode_sig_VeOUT_SP_PowerMode_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_SP_PowerMode_sig_VeOUT_SP_PowerMode_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeINP_HWA_SleepCommand_sig_VeINP_HWA_SleepCommand_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_HWA_SleepCommand_sig_VeINP_HWA_SleepCommand_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VeOUT_CMS_ZCULCarMode_sig_VeOUT_CMS_ZCULCarMode_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_CMS_ZCULCarMode_sig_VeOUT_CMS_ZCULCarMode_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_BSW_EEReady_flg_VbINP_BSW_EEReady_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_BSW_EEReady_flg_VbINP_BSW_EEReady_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_FLPsngManUp_flg_VbINP_HWA_FLPsngManUp_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_FLPsngManUp_flg_VbINP_HWA_FLPsngManUp_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_FLPsngManDn_flg_VbINP_HWA_FLPsngManDn_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_FLPsngManDn_flg_VbINP_HWA_FLPsngManDn_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_FLPsngAutoUp_flg_VbINP_HWA_FLPsngAutoUp_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_FLPsngAutoUp_flg_VbINP_HWA_FLPsngAutoUp_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_HWA_FLPsngAutoDn_flg_VbINP_HWA_FLPsngAutoDn_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_HWA_FLPsngAutoDn_flg_VbINP_HWA_FLPsngAutoDn_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeINP_CAN_ICMWashModeSwSts_sig_VeINP_CAN_ICMWashModeSwSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeINP_CAN_ICMWashModeSwSts_sig_VeINP_CAN_ICMWashModeSwSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_ICMCampingModeSwSts_flg_VbINP_CAN_ICMCampingModeSwSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_ICMCampingModeSwSts_flg_VbINP_CAN_ICMCampingModeSwSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_PEPSRKElockSts_flg_VbINP_CAN_PEPSRKElockSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_PEPSRKElockSts_flg_VbINP_CAN_PEPSRKElockSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_PEPSRKEunlockSts_flg_VbINP_CAN_PEPSRKEunlockSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_PEPSRKEunlockSts_flg_VbINP_CAN_PEPSRKEunlockSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbOUT_PDU_PowerModeValid_flg_VbOUT_PDU_PowerModeValid_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_PDU_PowerModeValid_flg_VbOUT_PDU_PowerModeValid_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VeOUT_ALM_ZCULAntiThelfSts_sig_VeOUT_ALM_ZCULAntiThelfSts_sig(UInt8 u);

extern Std_ReturnType Rte_Read_VeOUT_ALM_ZCULAntiThelfSts_sig_VeOUT_ALM_ZCULAntiThelfSts_sig(UInt8* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_ICMInCarCampingSwSts_flg_VbINP_CAN_ICMInCarCampingSwSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_ICMInCarCampingSwSts_flg_VbINP_CAN_ICMInCarCampingSwSts_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CFG_LeRiRudder_flg_VbINP_CFG_LeRiRudder_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CFG_LeRiRudder_flg_VbINP_CFG_LeRiRudder_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_DKMWindowClose_flg_VbINP_CAN_DKMWindowClose_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_DKMWindowClose_flg_VbINP_CAN_DKMWindowClose_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbINP_CAN_DKMWindowOpen_flg_VbINP_CAN_DKMWindowOpen_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbINP_CAN_DKMWindowOpen_flg_VbINP_CAN_DKMWindowOpen_flg(Boolean* u);

extern Std_ReturnType Rte_Write_VbOUT_CMS_NAPModeSts_flg_VbOUT_CMS_NAPModeSts_flg(Boolean u);

extern Std_ReturnType Rte_Read_VbOUT_CMS_NAPModeSts_flg_VbOUT_CMS_NAPModeSts_flg(Boolean* u);

extern FUNC(void, PWN_CODE) PWN_Init(void);

extern FUNC(void, PWN_CODE) PWN_Step(void);

extern void setDebugTxt(UInt8 cycle);
extern void setDebugOpen(void);
extern void setDebugClose(void);
extern void setCycle(UInt8 cycle);

#endif //SEAT_MODELTEST_RTENEW_H

# ifdef __cplusplus

}
#endif

