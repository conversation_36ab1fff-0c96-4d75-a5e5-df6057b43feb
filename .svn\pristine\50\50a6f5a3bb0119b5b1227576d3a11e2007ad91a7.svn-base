#include "gtest/gtest.h"
#include "TEST_API.h"
#include <stdio.h>
#include <iostream>
#include <string>
#include <math.h>
#include "enum.h"
using namespace std;

static void TEST_TEMP(int Test_1,string Test_2,boolean_T &Test_3,int time)
{
    int temp=Test_1;
    int num=0;
    for(int i=1;i<=time;i++)
    {
        TestDLKStep(1);
        if(temp!=Test_3)
        {
            temp=Test_3;
            cout<<"经过的时间为："<<i<<"ticks后"<<Test_2<<"的值变化为："<<temp<<"\n";
        }
    }
}

static void Crash()
{
    TestDLKStep(2);
    EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
    EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
    for(int i=0;i<6;i++)
    {
        TestDLKStep(20);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
        TestDLKStep(81);
        EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,1);
        EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,1);
    }
    TestDLKStep(20);
    EXPECT_EQ(VbOUT_DLK_DoorFLUnlock_flg,0);
    EXPECT_EQ(VbOUT_DLK_DoorPassUnlock_flg,0);
}

namespace
{
    TEST(DLK,SI_TC_DLK_001)
    {
        TEST_DLKINIT();
        //a. 中控锁状态为闭锁状态
        //b. 处于解防或预设防状态
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDebugEnable();
        VmOUT_ALM_AlarmState_enum=ALARM_PREARM1;
        //检测中控锁按键状态 DSP_CentralUnlockButtSts 从 0x0: No Press 变为 0x1: Press。
        TestDLKStep(100);
        VeINP_LIN_DSPCentralButtSts_sig=1;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);

        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);

        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);

        TestDLKStep(12);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);

        TestDLKStep(482);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(4);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(500);
        TestDebugDisable();

        //驱 动 左 前 门 电 机 和 乘 客 侧 电 机 执 行 解 锁 动 作 ， 并 发 送 触 发 源 为 中 控 解 锁
        //ZCUL_LockCmdSource=0x83:Central_Control_UnLock
    }

    TEST(DLK,SI_TC_DLK_002)
    {
        TEST_DLKINIT();
        TestDebugEnable();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(2);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(482);
        TestDebugDisable();
    }

    TEST(DLK,SI_TC_DLK_003)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VmOUT_ALM_AlarmState_enum = 4;//预设防
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_004)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_005)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_006)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_007)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_008)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_009)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_010)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_011)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_012)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_013)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_014)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_015)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_016)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_017)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_018)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_019)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_020)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 8);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 8);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(2);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x83);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_021)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_022)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_023)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_024)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_025)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_026)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_027)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_028)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_029)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_030)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_031)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_032)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_033)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_034)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_035)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_036)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(打开)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_037)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_038)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_039)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_040)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_041)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_042)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_043)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_044)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_045)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_046)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_047)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_048)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_049)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_050)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_051)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_052)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_053)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_054)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_055)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_056)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门打开、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_057)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 7);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_058)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_059)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_060)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速有效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_061)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_062)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_063)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_064)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态有效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_065)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_066)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_067)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_068)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 7);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 7);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 3);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_069)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_070)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_071)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_072)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_073)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_074)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_075)
    {
        TEST_DLKINIT();
        //电源非OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_076)
    {
        TEST_DLKINIT();
        //电源OFF(30)、四门关闭、左前门/中控锁(关闭)
        //车速无效且车速为“2”、电源模式状态无效
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_LIN_DSPCentralButtSts_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_LIN_DSPCentralButtSts_sig=3;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_077)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_078)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_079)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_080)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_081)
    {
        setDebugOpen();
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        setDebugClose();
    }

    TEST(DLK,SI_TC_DLK_082)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速无效、左前门解锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_083)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_084)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
//        VeOUT_ALM_ZCULAntiThelfSts_sig=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_085)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源无效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_086)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_087)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_088)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_089)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_090)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速无效、左前门解锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_091)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_092)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速无效、左前门闭锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_093)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速5、车速有效、左前门解锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_094)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、左前门解锁
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_095)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速2、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_096)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速5、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_097)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_098)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速0、车速有效、
        //左前门闭锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_099)
    {
        TEST_DLKINIT();
        //电源OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门打开
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_100)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_101)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速5、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_102)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_103)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门闭锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_104)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门打开
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_105)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_106)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速5、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_107)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_108)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门闭锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_109)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门打开
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 16);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_110)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_111)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速5、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbOUT_SP_ZCULFLDoorSts_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_112)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_113)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门闭锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_114)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速0、车速有效、
        //左前门解锁、四门打开
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_115)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_116)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速5、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_117)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速0、车速有效、
        //左前门解锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_118)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速0、车速有效、
        //左前门闭锁、四门关闭
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 48);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 48);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 4);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_119)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速0、车速有效、
        //左前门解锁、四门打开
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_NFCenterSmartCardvalid_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_NFCenterSmartCardvalid_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 49);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 49);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 132);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_120)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_121)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_122)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_123)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_124)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_125)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_126)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_127)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_128)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_129)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_130)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_131)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_132)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_133)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_134)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_135)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_136)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_137)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_138)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_139)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_140)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_141)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_142)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_143)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_144)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_145)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_146)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_147)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);////TODO
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_148)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_149)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_150)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_151)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_152)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_153)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_154)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_155)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_156)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_157)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_158)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_159)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_160)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_161)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_162)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_163)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_164)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_165)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_166)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_167)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_168)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_169)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_170)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_171)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_172)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_173)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_174)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 133);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_175)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_176)
    {
        TEST_DLKINIT();
        //档位在P档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 22);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 22);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_177)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_178)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_179)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 22);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 22);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_180)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_181)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 22);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 22);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_182)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 5);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_183)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_184)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_185)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_186)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_187)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_188)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_189)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_190)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_191)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_192)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_193)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_194)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_195)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_196)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_197)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_198)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_199)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_200)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_201)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_202)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_203)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //DLK的输出值
        VeINP_CAN_DKMFourDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_204)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_205)
    {
        setDebugOpen();
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_206)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_207)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_208)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_209)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_210)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_211)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_212)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_213)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_214)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_215)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_216)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_217)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_218)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_219)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_220)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_221)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_222)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_223)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_224)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_225)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_226)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_227)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_228)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_229)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_230)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_231)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速2、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_232)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速5、车速有效
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_233)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_234)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_235)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_236)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_237)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_238)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_239)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_240)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_241)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_242)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_243)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_244)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_245)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_246)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_247)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_248)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_249)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_250)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_251)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_252)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_253)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_254)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_255)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_256)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_257)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 24);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 24);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x88);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 136);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_258)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 54);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_259)
    {
        TEST_DLKINIT();
        //档位在P档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_260)
    {
        TEST_DLKINIT();
        //档位在P档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 54);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_261)
    {
        TEST_DLKINIT();
        //档位在P档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_262)
    {
        TEST_DLKINIT();
        //档位在P档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_263)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 54);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_264)
    {
        TEST_DLKINIT();
        //档位在N档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_265)
    {
        TEST_DLKINIT();
        //档位在N档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 54);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_266)
    {
        TEST_DLKINIT();
        //档位在N档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=0;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(96);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 8);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(500);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 17);
        TestDLKStep(1);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_267)
    {
        TEST_DLKINIT();
        //档位在N档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=2;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_268)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_269)
    {
        TEST_DLKINIT();
        //档位空、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_270)
    {
        TEST_DLKINIT();
        //档位空、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_271)
    {
        TEST_DLKINIT();
        //档位空、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_272)
    {
        TEST_DLKINIT();
        //档位空、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_273)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_274)
    {
        TEST_DLKINIT();
        //档位R档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_275)
    {
        TEST_DLKINIT();
        //档位R档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_276)
    {
        TEST_DLKINIT();
        //档位R档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_277)
    {
        TEST_DLKINIT();
        //档位R档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=3;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_278)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_279)
    {
        TEST_DLKINIT();
        //档位D档、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_280)
    {
        TEST_DLKINIT();
        //档位D档、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_281)
    {
        TEST_DLKINIT();
        //档位D档、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_282)
    {
        TEST_DLKINIT();
        //档位D档、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=4;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_283)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_284)
    {
        TEST_DLKINIT();
        //档位错误、车速5、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_285)
    {
        TEST_DLKINIT();
        //档位错误、车速0、车速有效、四门关闭
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=0;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=0;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_286)
    {
        TEST_DLKINIT();
        //档位错误、车速2、车速有效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_287)
    {
        TEST_DLKINIT();
        //档位错误、车速100、车速无效、四门打开
        VeINP_CAN_VCU1NActualGear_sig=15;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VbOUT_SP_4DoorAjar_flg=1;
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=0;
        TestDLKStep(103);

        //观测DLK输出是否对应
        VeINP_CAN_DKMDriverDoorLockControlAPP_sig=2;
        VbINP_HWA_FLDoorLockStsFb_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 54);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }


    TEST(DLK,SI_TC_DLK_288)
    {
        TEST_DLKINIT();
//靠近解锁使能状态(EE读取)为0、
//蓝牙控制命令请求四门解锁
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;

        VeINP_CAN_DKMBLECommandReq_sig=2;
        VbINP_CAN_ICMPollingUnlockCarReq_flg=0;
        TestDLKStep(103);

//观测DLK的输出是否符合预期
        VbINP_CAN_ICMPollingUnlockCarReq_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 1);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 1);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 1);
    }

    TEST(DLK,SI_TC_DLK_289)
    {
        TEST_DLKINIT();
//靠近解锁使能状态(EE读取)为0、
//蓝牙控制命令请求四门解锁
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;

        VeINP_CAN_DKMBLECommandReq_sig=2;
        VbINP_CAN_ICMPollingUnlockCarReq_flg=0;
        TestDLKStep(103);

//观测DLK的输出是否符合预期
        VbINP_CAN_ICMPollingUnlockCarReq_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingUnlockCarCarSts_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_290)
    {
        TEST_DLKINIT();
//电源OFF（30,15）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_291)
    {
        TEST_DLKINIT();
//电源OFF（30,15）、电源有效、车速5、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_292)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_293)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_294)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_295)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_296)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_297)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_298)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_299)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_300)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_301)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_302)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_303)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_304)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_305)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_306)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_307)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_308)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_309)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_310)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、OTA
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_311)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、OTA
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=0;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_312)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、OTA
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=2;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_313)
    {
        setDebugOpen();
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、远程
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        setDebugClose();
    }

    TEST(DLK,SI_TC_DLK_314)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、远程
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_315)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速2、车速有效、
//靠近解锁功能开启、远程
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_316)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源有效、车速100、车速无效、
//靠近解锁功能开启、远程
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_317)
    {
        TEST_DLKINIT();
//电源OFF（30,15）、电源无效、车速2、车速有效
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_318)
    {
        TEST_DLKINIT();
//电源OFF（30,15）、电源无效、车速5、车速有效
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 23);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 23);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 23);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x85);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_319)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum,31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_320)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_321)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_322)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_323)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_324)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_325)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeINP_CAN_VCU1NActualGear_sig=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLEAutomaticReq_sig=POLLING_LOCK;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_326)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_327)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_328)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLECommandReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 22);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_329)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeINP_CAN_DKMBLEAutomaticReq_sig=3;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_330)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_331)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_332)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_333)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_334)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_335)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_336)
    {
        TEST_DLKINIT();
//电源非OFF（30）、电源无效、车速2、车速有效、
//靠近解锁功能开启、不在OTA和远程模式
//
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

//观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_337)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_338)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_339)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_340)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_341)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_342)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能开启、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_343)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、
        //靠近解锁功能开启、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=1;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 31);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 31);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0x87);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 31);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(482);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 135);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
    }

    TEST(DLK,SI_TC_DLK_344)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_345)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源有效、车速5、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_346)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_347)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_348)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_349)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_350)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_351)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_352)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_353)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_354)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_355)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_356)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_357)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_358)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_359)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_360)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_361)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_362)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_363)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_364)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_365)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_366)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_367)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_368)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_369)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_370)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源有效、车速100、车速无效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=1;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_371)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_372)
    {
        TEST_DLKINIT();
        //电源OFF（30,15）、电源无效、车速5、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=0;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=5;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_373)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_374)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_375)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_376)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_377)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_378)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_379)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_380)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_381)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_382)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_383)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_384)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_385)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=0;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_386)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=1;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_387)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=4;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_388)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=5;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_389)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=6;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_390)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、不在OTA和远程模式
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=7;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_391)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_392)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_393)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、OTA
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=2;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_394)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=1;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_395)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=2;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_396)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速2、车速有效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=2;
        VbINP_CAN_EspVehSpdVld_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_397)
    {
        TEST_DLKINIT();
        //电源非OFF（30）、电源无效、车速100、车速无效、
        //靠近解锁功能关闭、远程
        //
        VeOUT_PDU_ZCULSystemPowerSource_sig=3;
        VeOUT_PDU_PowerMode_sig=3;
        VbOUT_PDU_PowerModeValid_flg=0;
        VeOUT_SP_EspVehSpd_kmh=100;
        VbINP_CAN_EspVehSpdVld_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=0;
        VbINP_EPRM_ZCULPollingUnlockCarCarStsFromEE_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期

        VeOUT_CMS_ZCULCarMode_sig=1;
        VeINP_CAN_DKMBLEAutomaticReq_sig=2;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VeOUT_DLK_ZCULLockCmdSource_sig, 0);
        TestDLKStep(100);
    }

    TEST(DLK,SI_TC_DLK_398)
    {

        TEST_DLKINIT();
        //靠近解锁使能状态(EE读取)为0、
        //蓝牙控制命令请求四门解锁
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=0;
        VeINP_CAN_DKMBLECommandReq_sig=3;
        VbINP_CAN_ICMPollingLockCarRed_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VeINP_CAN_DKMBLEAutomaticReq_sig=1;

        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
    }

    TEST(DLK,SI_TC_DLK_399)
    {
        TEST_DLKINIT();
        //靠近解锁使能状态(EE读取)为0、
        //蓝牙控制命令请求四门解锁
        VbINP_EPRM_ZCULPollingLockCarStsFromEE_flg=1;
        VeINP_CAN_DKMBLECommandReq_sig=3;
        VbINP_CAN_ICMPollingLockCarRed_flg=0;
        TestDLKStep(103);

        //观测DLK的输出是否符合预期
        VbINP_CAN_ICMPollingLockCarRed_flg=1;
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockDrt_enum, 0);
        EXPECT_EQ(VmOUT_DLK_LockSrc_enum, 0);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
        TestDLKStep(1);
        EXPECT_EQ(VmOUT_DLK_LockSucFb_enum, 0);
        TestDLKStep(13);
        EXPECT_EQ(VbOUT_DLK_ZCULPollingLockCarStsToEE_flg, 0);
    }
}
