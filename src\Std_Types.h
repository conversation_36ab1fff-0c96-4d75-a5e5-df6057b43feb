
#ifdef __cplusplus

extern "C" {

#endif

                
#ifndef STD_TYPES_H
#define STD_TYPES_H

/** \brief AUTOSAR Standard type header
 **
 ** This file contains the implementation of the Autosar
 ** Standard Types.
 ** See "Specification of Standard Types".
 **
 ** Do not edit this file manually.
 ** Any change might compromise the safety integrity level of
 ** the software partition it is contained in.
 **
 ** Product: SW-MCAL42-DRV
 **
 ** (c) 2017-2018, Cypress Semiconductor Corporation. All rights reserved.
 **
 ** Warranty and Disclaimer
 **
 ** This software product is property of Cypress Semiconductor Corporation or
 ** its subsidiaries.
 ** Any use and/or distribution rights for this software product are provided
 ** only under the Cypress Software License Agreement.
 ** Any use and/or distribution of this software product not in accordance with
 ** the terms of the Cypress Software License Agreement are unauthorized and
 ** shall constitute an infringement of Cypress intellectual property rights.
*/


/*==================[inclusions]=============================================*/

#include <Compiler.h>       /* Autosar compiler specific declarations */
#include <Platform_Types.h> /* Autosar platform specific type declarations */

/*==================[macros]=================================================*/

#if (defined STD_HIGH) /* guard to prevent double definition */
#error STD_HIGH already defined
#endif /* if (defined STD_HIGH) */

#define STD_HIGH 0x01U /** \brief physical state 5V or 3.3V  */

#if (defined STD_LOW) /* guard to prevent double definition */
#error STD_LOW already defined
#endif /* if (defined STD_LOW) */

#define STD_LOW 0x00U /** \brief physical state 0V */

#if (defined STD_ACTIVE) /* guard to prevent double definition */
#error STD_ACTIVE already defined
#endif /* if (defined STD_ACTIVE) */

#define STD_ACTIVE 0x01U /** \brief Autosar logical state 'active' */

#if (defined STD_IDLE) /* guard to prevent double definition */
#error STD_IDLE already defined
#endif /* if (defined STD_IDLE) */

#define STD_IDLE 0x00U /** \brief Autosar logical state 'idle' */

#if (defined STD_ON) /* guard to prevent double definition */
#error STD_ON already defined
#endif /* if (defined STD_ON) */

#define STD_ON 0x01U /** \brief Autosar definition for 'on' */

#if (defined STD_OFF) /* guard to prevent double definition */
#error STD_OFF already defined
#endif /* if (defined STD_OFF) */

#define STD_OFF 0x00U /** \brief Autosar definition for 'off' */

#if (!defined STATUSTYPEDEFINED)
#define STATUSTYPEDEFINED
#endif /* if (!defined STATUSTYPEDEFINED) */

typedef unsigned char StatusType; /* OSEK compliance */

#if (!defined E_OK)
#define E_OK 0x00U
#endif /* if (!defined E_OK) */

#if (!defined E_NOT_OK) /* guard to prevent double definition */
#define E_NOT_OK 0x01U
#endif /* if (!defined E_NOT_OK) */
/*------------------[Autosar vendor identification]--------------------------*/

#if (!defined STD_VENDOR_ID) /* check for double definition */
/** \brief Autosar module vendor identification
 **
 ** Vendor ID of the dedicated implementation of this module according
 ** to the AUTOSAR vendor list. */
#define STD_VENDOR_ID 66U /* Cypress */
#endif /* if (!defined STD_VENDOR_ID) */

#if (!defined STD_MODULE_ID) /* check for double definition */
/** \brief defines the module ID */
#define STD_MODULE_ID 197U
#endif /* if (!defined STD_MODULE_ID) */

/*------------------[standard version declarations]--------------------------*/

#if (defined STD_AR_RELEASE_MAJOR_VERSION) /* guard to prevent double definition */
#error STD_AR_RELEASE_MAJOR_VERSION already defined
#endif /*  if (defined STD_AR_RELEASE_MAJOR_VERSION) */

/** \brief definition of a major version of the standard types */
#define STD_AR_RELEASE_MAJOR_VERSION 0x04U

#if (defined STD_AR_RELEASE_MINOR_VERSION) /* guard to prevent double definition */
#error STD_AR_RELEASE_MINOR_VERSION already defined
#endif /* if (defined STD_AR_RELEASE_MINOR_VERSION) */

/** \brief definition of a minor version of the standard types */
#define STD_AR_RELEASE_MINOR_VERSION 0x02U

#if (defined STD_AR_RELEASE_REVISION_VERSION) /* guard to prevent double definition */
#error STD_AR_RELEASE_REVISION_VERSION already defined
#endif /* if (defined STD_AR_RELEASE_REVISION_VERSION) */

/** \brief definition of a patch version of the standard types */
#define STD_AR_RELEASE_REVISION_VERSION 0x02U

/*------------------[Autosar software version]-------------------------------*/

#if (!defined STD_SW_MAJOR_VERSION) /* check for double definition */
/** \brief definition of the major version of this implementation */
#define STD_SW_MAJOR_VERSION 1U
#endif /*  if (!defined STD_SW_MAJOR_VERSION) */

#if (!defined STD_SW_MINOR_VERSION) /* check for double definition */
/** \brief definition of a minor version of this implementation */
#define STD_SW_MINOR_VERSION 6U
#endif /* if (!defined STD_SW_MINOR_VERSION) */

#if (!defined STD_SW_PATCH_VERSION) /* check for double definition */
/** \brief definition of a patch version of this implementation */
#define STD_SW_PATCH_VERSION 0U
#endif /* if (!defined STD_SW_PATCH_VERSION) */

/*==================[type definitions]=======================================*/

/** \brief Autosar standard API return type */
typedef uint8 Std_ReturnType;

/** \brief return type for <Module_name>_GetVersionInfo() calls
 **
 ** This type is used to request the version of BSW module using the
 ** <Module_name>_GetVersionInfo() function. */
typedef struct
{
   uint16 vendorID;
   uint16 moduleID;
/* Modification for update from AR_3.1.4 to AR_4.0.3: Delete Instance ID field */
   uint8  sw_major_version;
   uint8  sw_minor_version;
   uint8  sw_patch_version;
} Std_VersionInfoType;

/*==================[external function declarations]=========================*/

/*==================[internal function declarations]=========================*/

/*==================[external constants]=====================================*/

/*==================[internal constants]=====================================*/

/*==================[external data]==========================================*/

/*==================[internal data]==========================================*/

/*==================[external function definitions]==========================*/

/*==================[internal function definitions]==========================*/

#endif /* ifndef STD_TYPES_H */
/*==================[end of file]============================================*/

                    
#ifdef __cplusplus

}
#endif
                