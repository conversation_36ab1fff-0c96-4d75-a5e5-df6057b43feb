/*
 * const_params.c
 *
 * Code generation for model "Enh_Window_L".
 *
 * Model version              : 1.29
 * Simulink Coder version : 9.7 (R2022a) 13-Nov-2021
 * C source code generated on : Thu May 25 16:42:21 2023
 */
#include "rtwtypes.h"

extern const uint32 rtCP_pooled_8dmTEHTGbN1O[2];
const uint32 rtCP_pooled_8dmTEHTGbN1O[2] = { 30U, 30U } ;

extern const uint8 rtCP_pooled_QmT7cJz1beFe[961];
const uint8 rtCP_pooled_QmT7cJz1beFe[961] = { 0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U,
  8U, 9U, 0U, 11U, 12U, 13U, 14U, 0U, 16U, 17U, 18U, 19U, 0U, 21U, 22U, 23U, 24U,
  0U, 26U, 27U, 28U, 29U, 0U, 1U, 1U, 0U, 1U, 0U, 0U, 1U, 0U, 1U, 0U, 0U, 1U, 1U,
  1U, 1U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 2U, 2U,
  0U, 2U, 0U, 0U, 2U, 0U, 2U, 0U, 0U, 2U, 2U, 2U, 2U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 3U, 3U, 0U, 3U, 0U, 0U, 3U, 0U, 3U, 0U, 0U,
  3U, 3U, 3U, 3U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  4U, 4U, 0U, 4U, 0U, 0U, 4U, 0U, 4U, 0U, 0U, 4U, 4U, 4U, 4U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 2U, 0U, 4U, 0U, 6U, 7U, 8U,
  9U, 0U, 11U, 12U, 13U, 14U, 0U, 16U, 17U, 18U, 19U, 0U, 21U, 22U, 23U, 24U, 0U,
  26U, 27U, 28U, 29U, 0U, 6U, 6U, 0U, 6U, 0U, 0U, 6U, 0U, 6U, 0U, 0U, 6U, 6U, 6U,
  6U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 7U, 7U, 0U,
  7U, 0U, 0U, 7U, 0U, 7U, 0U, 0U, 7U, 0U, 0U, 7U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 8U, 8U, 0U, 8U, 0U, 0U, 8U, 0U, 8U, 0U, 0U, 8U,
  8U, 8U, 8U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 9U,
  9U, 0U, 9U, 0U, 0U, 9U, 0U, 9U, 0U, 0U, 9U, 9U, 9U, 9U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U, 2U, 3U, 4U, 0U, 0U, 7U, 0U, 9U,
  0U, 11U, 12U, 13U, 14U, 0U, 16U, 17U, 18U, 19U, 0U, 21U, 22U, 23U, 24U, 0U,
  26U, 27U, 28U, 29U, 0U, 11U, 0U, 0U, 3U, 4U, 0U, 6U, 0U, 8U, 9U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 12U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 13U, 1U, 0U, 3U, 4U, 0U, 6U, 0U, 8U,
  9U, 0U, 13U, 13U, 13U, 13U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 14U, 1U, 0U, 3U, 4U, 0U, 6U, 0U, 8U, 9U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U, 2U, 3U, 4U,
  0U, 6U, 7U, 8U, 9U, 0U, 0U, 12U, 0U, 14U, 0U, 16U, 17U, 18U, 19U, 0U, 21U, 22U,
  23U, 24U, 0U, 26U, 27U, 28U, 29U, 0U, 16U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 17U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 18U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 19U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U, 2U,
  3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 14U, 0U, 0U, 17U, 0U, 19U, 0U,
  21U, 22U, 23U, 24U, 0U, 26U, 27U, 28U, 29U, 0U, 21U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 22U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 23U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 24U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 14U, 0U, 16U, 17U,
  18U, 19U, 0U, 0U, 22U, 0U, 24U, 0U, 26U, 27U, 28U, 29U, 0U, 26U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 27U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  28U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 29U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 1U, 2U, 3U, 4U, 0U, 6U, 7U, 8U, 9U, 0U, 11U, 12U, 13U, 14U,
  0U, 16U, 17U, 18U, 19U, 0U, 21U, 22U, 23U, 24U, 0U, 0U, 27U, 0U, 29U, 0U } ;

extern const uint8 rtCP_pooled_p8lYcUr40KT5[31];
const uint8 rtCP_pooled_p8lYcUr40KT5[31] = { 0U, 1U, 2U, 3U, 4U, 5U, 6U, 7U,
  8U, 9U, 10U, 11U, 12U, 13U, 14U, 15U, 16U, 17U, 18U, 19U, 20U, 21U, 22U, 23U,
  24U, 25U, 26U, 27U, 28U, 29U, 30U } ;

extern const uint8 rtCP_pooled_qVOroVzgPVdp[168];
const uint8 rtCP_pooled_qVOroVzgPVdp[168] = { 0U, 255U, 255U, 2U, 255U, 255U,
                                              255U, 0U, 255U, 255U, 0U, 255U, 255U, 255U, 0U, 255U, 255U, 2U, 255U, 255U,
                                              255U, 0U, 255U, 255U, 4U, 255U, 255U, 255U, 0U, 2U, 2U, 2U, 2U, 2U, 2U, 0U, 2U,
                                              2U, 2U, 2U, 1U, 0U, 0U, 40U, 40U, 40U, 40U, 40U, 0U, 0U, 40U, 40U, 40U, 40U,
                                              40U, 0U, 0U, 1U, 2U, 3U, 3U, 28U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 2U, 2U,
                                              2U, 2U, 1U, 1U, 0U, 4U, 4U, 4U, 4U, 4U, 4U, 0U, 255U, 255U, 255U, 255U, 2U, 2U,
                                              0U, 255U, 255U, 255U, 255U, 1U, 0U, 0U, 255U, 255U, 255U, 255U, 40U, 0U, 0U,
                                              255U, 255U, 255U, 255U, 40U, 0U, 0U, 255U, 255U, 255U, 255U, 28U, 0U, 0U, 255U,
                                              255U, 255U, 255U, 0U, 0U, 0U, 255U, 255U, 255U, 255U, 1U, 1U, 0U, 255U, 255U,
                                              255U, 255U, 4U, 4U, 0U, 0U, 0U, 255U, 255U, 255U, 255U, 0U, 1U, 2U, 255U, 255U,
                                              255U, 255U, 0U, 1U, 1U, 255U, 255U, 255U, 255U, 0U, 4U, 4U, 255U, 255U, 255U,
                                              255U } ;
