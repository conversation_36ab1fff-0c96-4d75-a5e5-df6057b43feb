#include "TESTCASE_PWN.h"

namespace
{
    TEST(PWN,SI_TC_PWN_901)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xA;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xA;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xA;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xA;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_902)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=1;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=1;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=1;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=1;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_903)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=2;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=2;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=2;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=2;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_904)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=3;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=3;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_905)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=4;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=4;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=4;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=4;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_906)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=5;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=5;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=5;////语音控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_907)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=6;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=6;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=6;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=6;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_908)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=7;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=7;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=7;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=7;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_909)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=8;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=8;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=8;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=8;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_910)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=9;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=9;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=9;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=9;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_911)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xB;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xB;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xB;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xB;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_912)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xA;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xA;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xA;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xA;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_913)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xA;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xA;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xA;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xA;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_914)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=1;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=1;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=1;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=1;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_915)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=2;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=2;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=2;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=2;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_916)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=3;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=3;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=3;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=3;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_917)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=4;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=4;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=4;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=4;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_918)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=5;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=5;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=5;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=5;////语音控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_919)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=6;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=6;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=6;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=6;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_920)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=7;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=7;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=7;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=7;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_921)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=8;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=8;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=8;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=8;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_922)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=9;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=9;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=9;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=9;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_923)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xB;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xB;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xB;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xB;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_924)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=0xA;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=0xA;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=0xA;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=0xA;////语音控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_925)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=1;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=1;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=1;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=1;////语音控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_926)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(100);
        UInt16 Rinal_output=Thermal_protection(*RL_WndSwSts_Switch,1,*RL_WndSwSts_Switch,2,*RL_FeedBack,1,2,10,*RL_MovT_Status);////左后触发热保护;
        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=1;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=1;////语音控制左后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 64);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_927)
    {
        TEST_PWN_SILINIT();//////FRS_686;//////7.2 语音升降车窗-左前/左后-Master;//////FRS_687;//////7.3 语音升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：语音不动作;
        VbINP_HWA_FLAntipinchEnable_flg=0;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=0;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=0;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=0;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_ICMFLWindowsVoiControl_sig=1;////语音控制左前车窗;
        VeINP_CAN_ICMRLWindowsVoiControl_sig=1;////语音控制左后车窗;
        VeINP_CAN_ICMFRWindowsVoiControl_sig=1;////语音控制右前车窗;
        VeINP_CAN_ICMRRWindowsVoiControl_sig=1;////语音控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2ICM_sig, 48059);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_928)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xA;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xA;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xA;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xA;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_929)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=1;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=1;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=1;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=1;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_930)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=2;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=2;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=2;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=2;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_931)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=3;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=3;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_932)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=4;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=4;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=4;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=4;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_933)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=5;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=5;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=5;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=5;////蓝牙控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_934)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=6;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=6;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=6;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=6;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_935)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=7;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=7;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=7;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=7;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_936)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=8;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=8;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=8;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=8;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_937)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=9;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=9;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=9;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=9;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_938)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xB;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xB;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xB;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xB;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_939)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xA;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xA;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xA;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xA;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_940)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xA;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xA;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xA;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xA;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_941)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=1;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=1;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=1;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=1;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_942)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=2;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=2;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=2;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=2;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_943)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=3;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=3;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=3;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=3;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_944)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=4;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=4;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=4;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=4;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_945)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=5;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=5;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=5;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=5;////蓝牙控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_946)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=6;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=6;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=6;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=6;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_947)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=7;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=7;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=7;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=7;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_948)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=8;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=8;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=8;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=8;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_949)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=9;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=9;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=9;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=9;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_950)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xB;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xB;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xB;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xB;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_951)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=0xA;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=0xA;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=0xA;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=0xA;////蓝牙控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_952)
    {
        TEST_PWN_SILINIT();//////FRS_688;//////7.4 蓝牙升降车窗-左前/左后-Master;//////FRS_689;//////7.5 蓝牙升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：蓝牙不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;

        TestPWNStep(20);
        VeINP_CAN_DKMVCCMFLWindowsControl_sig=1;////蓝牙控制左前车窗;
        VeINP_CAN_DKMVCCMRLWindowsControl_sig=1;////蓝牙控制左后车窗;
        VeINP_CAN_DKMVCCMFRWindowsControl_sig=1;////蓝牙控制右前车窗;
        VeINP_CAN_DKMVCCMRRWindowsControl_sig=1;////蓝牙控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2DKM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2DKM_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_955)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xA;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xA;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xA;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xA;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_956)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_957)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=2;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=2;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=2;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=2;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_958)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=3;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=3;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=3;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=3;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_959)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=4;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=4;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=4;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=4;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_960)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=5;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=5;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=5;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=5;////远程控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_961)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=6;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=6;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=6;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=6;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_962)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=7;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=7;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=7;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=7;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_963)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=8;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=8;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=8;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=8;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_964)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=9;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=9;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=9;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=9;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_965)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xB;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xB;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xB;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xB;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_966)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xA;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xA;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xA;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xA;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_967)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xA;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xA;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xA;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xA;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1500;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1500;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=101;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=101;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 101);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_968)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 11);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=150;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=150;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 11);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 11);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_969)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=2;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=2;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=2;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=2;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 21);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=20;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=20;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 21);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 21);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_970)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=3;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=3;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=3;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=3;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 31);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=450;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=450;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=30;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=30;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 31);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 31);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_971)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=4;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=4;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=4;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=4;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 41);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=600;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=600;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=40;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=40;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 41);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 41);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_972)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=5;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=5;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=5;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=5;////远程控制右后车窗;
        TestPWNStep(75);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(125);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_973)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=6;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=6;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=6;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=6;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 61);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=900;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=900;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=60;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=60;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 61);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 61);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_974)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=7;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=7;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=7;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=7;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 71);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1050;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1050;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=70;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=70;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 71);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 71);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_975)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=8;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=8;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=8;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=8;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 81);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1200;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1200;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=80;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=80;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 81);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 81);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_976)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=9;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=9;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=9;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=9;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 91);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1350;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1350;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=90;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=90;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 1);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 4112);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 91);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 91);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_977)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程升窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=0;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xB;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xB;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xB;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xB;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=1;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=1;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=1;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=1;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 3);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 1);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=0;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=0;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=100;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=100;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 1);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 1);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_978)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程降窗;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=0xA;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=0xA;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=0xA;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=0xA;////远程控制右后车窗;
        VeINP_HWA_FLFeedBackRunSts_sig=2;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=2;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=2;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=2;////右后车窗运动状态反馈;
        TestPWNStep(15);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 4);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 4);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 3);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 101);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 101);

        TestPWNStep(10);
        VuINP_HWA_FLWinPostion_sig=1300;////驾驶侧车窗位置信号;
        VuINP_HWA_RLWinPostion_sig=1300;////左后车窗位置信号;
        VeINP_CAN_VCCMFRWindowStatus_sig=10;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=10;////RR车窗状态反馈（位置反馈）;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_CAN_VCCMFRWindowsMovSt_sig=0;////右前车窗运动状态反馈;
        VeINP_CAN_VCCMRRWindowsMovSt_sig=0;////右后车窗运动状态反馈;
        TestPWNStep(50);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 2);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 8224);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

        TestPWNStep(30);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 87);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 87);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_979)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=2;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2TCP_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_980)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(100);
        UInt16 Rinal_output=Thermal_protection(*RL_WndSwSts_Switch,1,*RL_WndSwSts_Switch,2,*RL_FeedBack,1,2,10,*RL_MovT_Status);////左后触发热保护;
        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 3);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 64);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_981)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=0;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=0;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=0;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=0;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=1;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 0);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 48059);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_982)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=0;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_983)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=2;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_984)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=3;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_985)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=4;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_986)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=5;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }

    TEST(PWN,SI_TC_PWN_987)
    {
        TEST_PWN_SILINIT();//////FRS_690;//////7.6 远程升降车窗-左前/左后-Master;//////FRS_691;//////7.7 远程升降车窗-右前/右后-Master;//////FRS_692;//////7.10 四车窗升降控制-右前/右后-Slave;//////车窗：远程不动作;
        VbINP_HWA_FLAntipinchEnable_flg=1;////左前防夹使能状态;
        VbINP_HWA_RLAntipinchEnable_flg=1;////左后防夹使能状态;
        VeINP_CAN_VCU1NActualGear_sig=1;////档位;
        VbINP_CAN_VCCMFRWinInitializedSts_flg=1;////右前车窗初始化状态反馈;
        VbINP_CAN_VCCMRRWinInitializedSts_flg=1;////右后车窗初始化状态反馈;
        VuINP_HWA_FLWinPostion_sig=750;////驾驶侧车窗位置信号;
        VuINP_HWA_FLWinPosMax_sig=1500;////驾驶侧车窗反馈代码识别到的最大位置;
        VuINP_HWA_RLWinPostion_sig=750;////左后车窗位置信号;
        VuINP_HWA_RLWinPosMax_sig=1500;////左后车窗反馈代码识别到的最大位置;
        VeINP_HWA_FLFeedBackRunSts_sig=0;////驾驶侧电机反馈运行状态;
        VeINP_HWA_FLStallSts_sig=0;////驾驶侧电机堵转状态;
        VeINP_HWA_RLFeedBackRunSts_sig=0;////左后电机反馈运行状态;
        VeINP_HWA_RLStallSts_sig=0;////左后电机堵转状态;
        VeINP_HWA_Voltage_100mV=120;////电池电压AD值;
        VeOUT_SP_PowerMode_sig=1;////电源模式;
        VeINP_CAN_ICMOTASts_sig=0;////OTA模式;
        VbINP_BSW_EEReady_flg=1;////EEReady;
        VeINP_CAN_VCCMFRWindowStatus_sig=50;////FR车窗状态反馈（位置反馈）;
        VeINP_CAN_VCCMRRWindowStatus_sig=50;////RR车窗状态反馈（位置反馈）;
        VeOUT_CMS_ZCULCarMode_sig=6;////车辆模式;

        TestPWNStep(20);
        VeINP_CAN_TCPFLWindowsControl_sig=1;////远程控制左前车窗;
        VeINP_CAN_TCPRLWindowsControl_sig=1;////远程控制左后车窗;
        VeINP_CAN_TCPFRWindowsControl_sig=1;////远程控制右前车窗;
        VeINP_CAN_TCPRRWindowsControl_sig=1;////远程控制右后车窗;
        TestPWNStep(10);
        EXPECT_EQ(VeOUT_PWN_ZCULFLWindowStatus_sig, 51);
        EXPECT_EQ(VeOUT_PWN_ZCULRLWindowStatus_sig, 51);
        EXPECT_EQ(VmOUT_PWN_FLOutMovTypeState_enum, 0);
        EXPECT_EQ(VmOUT_PWN_RLOutMovTypeState_enum, 0);
        EXPECT_EQ(VbOUT_PWN_ZCULFLWinInitializedSts_flg, 1);
        EXPECT_EQ(VbOUT_PWN_ZCULRLWinInitializedSts_flg, 1);
        EXPECT_EQ(VeOUT_PWN_PWNStatusResponse2ICM_sig, 0);
        EXPECT_EQ(VnOUT_PWN_PWNFailReason2TCP_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULFRWindowsControl_sig, 0);
        EXPECT_EQ(VeOUT_PWN_ZCULRRWindowsControl_sig, 0);

    }


}
