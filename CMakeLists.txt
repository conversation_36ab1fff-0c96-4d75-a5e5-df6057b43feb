cmake_minimum_required(VERSION 3.17)
project(ICAR05_L_OT_ModelTest)
set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -fprofile-arcs -ftest-coverage -fPIC")
set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c99 -fprofile-arcs -ftest-coverage -fPIC ")

set(CMAKE_CXX_STANDARD 14)

# 查找并配置 Google Test
find_package(GTest REQUIRED)

link_directories(./lib)
aux_source_directory(./testcase DIR_TESTCASE)
aux_source_directory(./src DIR_SRC)
add_executable(ICAR05_L_OT_ModelTest
        ${DIR_TESTCASE}
        ${DIR_SRC} testcase/TESTCASE_PWN.h)

include_directories(.)
include_directories(./src)
include_directories(${GTEST_INCLUDE_DIRS})

# 链接 Google Test 和其他必要的库
target_link_libraries(ICAR05_L_OT_ModelTest 
    PRIVATE 
    GTest::gtest 
    GTest::gtest_main
    -lPWN
    -lRTE
    -lTEST_API
)
