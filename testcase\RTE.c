#ifdef __cplusplus

extern "C" {
#endif
#include "RTE.h"
#include "Rte_Type.h"

#include "stdio.h"

FILE * fp_output;
float Timestamp = 0;
UInt8 cycle = 0;

#include "PWNL.h"
#include "PWNL.h"

UInt8 g_Req;
UInt8 g_Response;
static UInt8 printfCount = 0;

typedef struct _checkValueStruct
{
    UInt8 VeOUT_PWN_ZCULFLWindowStatus_sig_old;
    UInt8 VeOUT_PWN_ZCULRLWindowStatus_sig_old;
    Boolean VbOUT_PWN_ZCULDisablePassengerWndSts_flg_old;
    UInt8 VmOUT_PWN_FLOutMovTypeState_enum_old;
    UInt8 VmOUT_PWN_RLOutMovTypeState_enum_old;
    UInt8 VmOUT_PWN_FLOutMotorState_enum_old;
    UInt8 VmOUT_PWN_RLOutMotorState_enum_old;
    Boolean VbOUT_PWN_FLDrvBultStuck_flg_old;
    Boolean VbOUT_PWN_FRDrvBultStuck_flg_old;
    Boolean VbOUT_PWN_RLDrvBultStuck_flg_old;
    Boolean VbOUT_PWN_RRDrvBultStuck_flg_old;
    Boolean VbOUT_PWN_RLPsgBultStuck_flg_old;
    Boolean VbOUT_PWN_ZCULFLWinInitializedSts_flg_old;
    Boolean VbOUT_PWN_ZCULRLWinInitializedSts_flg_old;
    Boolean VbOUT_PWN_DisablePassengerWndStsToEE_flg_old;
    UInt8 VeOUT_PWN_PWNStatusResponse2ICM_sig_old;
    UInt8 VeOUT_PWN_PWNStatusResponse2TCP_sig_old;
    UInt8 VeOUT_PWN_PWNStatusResponse2DKM_sig_old;
    Boolean VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_old;
    Boolean VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_old;
    UInt8 VeOUT_PWN_ZCULFRWindowsControl_sig_old;
    UInt8 VeOUT_PWN_ZCULRRWindowsControl_sig_old;
    UInt8 VeOUT_PWN_ZCULFRWINCtrlCmd_sig_old;
    UInt8 VeOUT_PWN_ZCULRRWINCtrlCmd_sig_old;
    Boolean VbOUT_PWN_ALMWinUpError_flg_old;
    Boolean VbOUT_PWN_AllWinCloseStsFb_flg_old;
    UInt8 VeOUT_PWN_FLWinSrcHst_Array_old[10];
    UInt8 VeOUT_PWN_RLWinSrcHst_Array_old[10];
    Boolean VbOUT_PWN_FLWinOhpmode_flg_old;
    Boolean VbOUT_PWN_RLWinOhpmode_flg_old;
    Boolean VbOUT_PWN_FLForceWinCloseReq_flg_old;
    Boolean VbOUT_PWN_RLForceWinCloseReq_flg_old;
    UInt8 VeOUT_PWN_ZCULArmedCloseWndSts_sig_old;
    UInt8 VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_old;
    Boolean VbOUT_PWN_ZCULWindowSts_flg_old;
    UInt8 VeOUT_PWN_ZCULSourceCMDFR_sig_old;
    UInt8 VeOUT_PWN_ZCULSourceCMDRR_sig_old;
    UInt32 VnOUT_PWN_PWNFailReason2ICM_sig_old;
    UInt32 VnOUT_PWN_PWNFailReason2TCP_sig_old;
    UInt32 VnOUT_PWN_PWNFailReason2DKM_sig_old;
    UInt8 VeOUT_PWN_ZCULSourcebackFL_sig_old;
    UInt8 VeOUT_PWN_ZCULSourcebackRL_sig_old;
    Boolean VbOUT_PWN_SleepPermit_flg_old;
    UInt8 VeINP_LIN_DSPFLWndSwSts_sig_old;
    UInt8 VeINP_LIN_DSPFRWndSwSts_sig_old;
    UInt8 VeINP_LIN_DSPRLWndSwSts_sig_old;
    UInt8 VeINP_LIN_DSPRRWndSwSts_sig_old;
    Boolean VbINP_HWA_RLPsngManUp_flg_old;
    Boolean VbINP_HWA_RLPsngManDn_flg_old;
    Boolean VbINP_HWA_RLPsngAutoUp_flg_old;
    Boolean VbINP_HWA_RLPsngAutoDn_flg_old;
    Boolean VbOUT_SP_ZCULFLDoorSts_flg_old;
    Boolean VbOUT_SP_ZCULRLDoorSts_flg_old;
    UInt8 VeINP_HWA_Voltage_100mV_old;
    UInt8 VeINP_HWA_FLFeedBackRunSts_sig_old;
    UInt8 VeINP_HWA_RLFeedBackRunSts_sig_old;
    UInt8 VeINP_HWA_FLStallSts_sig_old;
    UInt8 VeINP_HWA_RLStallSts_sig_old;
    UInt16 VuINP_HWA_FLWinPostion_sig_old;
    UInt16 VuINP_HWA_RLWinPostion_sig_old;
    Boolean VbINP_HWA_FLAntipinchEnable_flg_old;
    Boolean VbINP_HWA_RLAntipinchEnable_flg_old;
    UInt16 VuINP_HWA_FLWinPosMax_sig_old;
    UInt16 VuINP_HWA_RLWinPosMax_sig_old;
    Boolean VbINP_CAN_ICMDisablePassengerWndReq_flg_old;
    Boolean VbINP_EPRM_DisablePassengerWndStsFromEE_flg_old;
    Boolean VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_old;
    Boolean VbINP_CAN_VCCMFRDoorSts_flg_old;
    Boolean VbINP_CAN_VCCMRRDoorSts_flg_old;
    Boolean VbINP_CAN_VCCMTrunkDoorSts_flg_old;
    UInt8 VeINP_CAN_ICMFLWindowsVoiControl_sig_old;
    UInt8 VeINP_CAN_ICMRLWindowsVoiControl_sig_old;
    UInt8 VeINP_CAN_ICMFRWindowsVoiControl_sig_old;
    UInt8 VeINP_CAN_ICMRRWindowsVoiControl_sig_old;
    UInt8 VeINP_CAN_TCPFLWindowsControl_sig_old;
    UInt8 VeINP_CAN_TCPRLWindowsControl_sig_old;
    UInt8 VeINP_CAN_TCPFRWindowsControl_sig_old;
    UInt8 VeINP_CAN_TCPRRWindowsControl_sig_old;
    UInt8 VeINP_CAN_DKMVCCMFLWindowsControl_sig_old;
    UInt8 VeINP_CAN_DKMVCCMRLWindowsControl_sig_old;
    UInt8 VeINP_CAN_DKMVCCMFRWindowsControl_sig_old;
    UInt8 VeINP_CAN_DKMVCCMRRWindowsControl_sig_old;
    UInt8 VeINP_CAN_ICMArmedCloseWndReq_sig_old;
    UInt8 VeINP_CAN_RLSWinCloseCmd_sig_old;
    Boolean VbINP_CAN_ICMRainAutoClosedWndReq_flg_old;
    UInt8 VeOUT_PDU_ZCULSystemPowerSource_sig_old;
    Boolean VbINP_CAN_VCCMFRWinInitializedSts_flg_old;
    Boolean VbINP_CAN_VCCMRRWinInitializedSts_flg_old;
    UInt8 VeINP_CAN_VCCMFRWindowStatus_sig_old;
    UInt8 VeINP_CAN_VCCMRRWindowStatus_sig_old;
    UInt8 VeINP_CAN_VCCMFRWindowsMovSt_sig_old;
    UInt8 VeINP_CAN_VCCMRRWindowsMovSt_sig_old;
    Boolean VbINP_CAN_VCCMFRAntipinchSts_flg_old;
    Boolean VbINP_CAN_VCCMRRAntipinchSts_flg_old;
    UInt8 VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_old;
    UInt8 VeINP_CAN_VCCMSourcebackFR_sig_old;
    UInt8 VeINP_CAN_VCCMSourcebackRR_sig_old;
    UInt8 VeINP_CAN_VCCMRunErrCauseFR_sig_old;
    UInt8 VeINP_CAN_VCCMRunErrCauseRR_sig_old;
    UInt8 VmOUT_ALM_AlarmState_enum_old;
    UInt8 VmOUT_ALM_PWNReq_enum_old;
    UInt8 VeINP_CAN_VCU1NActualGear_sig_old;
    UInt8 VeINP_HWA_FLWindowErrSource_sig_old;
    UInt8 VeINP_HWA_RLWindowErrSource_sig_old;
    UInt8 VeINP_CAN_ICMOTASts_sig_old;
    UInt8 VeOUT_SP_PowerMode_sig_old;
    UInt8 VeINP_HWA_SleepCommand_sig_old;
    UInt8 VeOUT_CMS_ZCULCarMode_sig_old;
    Boolean VbINP_BSW_EEReady_flg_old;
    Boolean VbINP_HWA_FLPsngManUp_flg_old;
    Boolean VbINP_HWA_FLPsngManDn_flg_old;
    Boolean VbINP_HWA_FLPsngAutoUp_flg_old;
    Boolean VbINP_HWA_FLPsngAutoDn_flg_old;
    UInt8 VeINP_CAN_ICMWashModeSwSts_sig_old;
    Boolean VbINP_CAN_ICMCampingModeSwSts_flg_old;
    Boolean VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_old;
    Boolean VbINP_CAN_PEPSRKElockSts_flg_old;
    Boolean VbINP_CAN_PEPSRKEunlockSts_flg_old;
    Boolean VbOUT_PDU_PowerModeValid_flg_old;
    UInt8 VeOUT_ALM_ZCULAntiThelfSts_sig_old;
    Boolean VbINP_CAN_ICMInCarCampingSwSts_flg_old;
    Boolean VbINP_CFG_LeRiRudder_flg_old;
    Boolean VbINP_CAN_DKMWindowClose_flg_old;
    Boolean VbINP_CAN_DKMWindowOpen_flg_old;
    Boolean VbOUT_CMS_NAPModeSts_flg_old;
}mCheckValueStruct;

static mCheckValueStruct checkStruct;

UInt8 VeOUT_PWN_ZCULFLWindowStatus_sig = 0;
UInt8 VeOUT_PWN_ZCULRLWindowStatus_sig = 0;
Boolean VbOUT_PWN_ZCULDisablePassengerWndSts_flg = false;
UInt8 VmOUT_PWN_FLOutMovTypeState_enum = 0;
UInt8 VmOUT_PWN_RLOutMovTypeState_enum = 0;
UInt8 VmOUT_PWN_FLOutMotorState_enum = 0;
UInt8 VmOUT_PWN_RLOutMotorState_enum = 0;
Boolean VbOUT_PWN_FLDrvBultStuck_flg = false;
Boolean VbOUT_PWN_FRDrvBultStuck_flg = false;
Boolean VbOUT_PWN_RLDrvBultStuck_flg = false;
Boolean VbOUT_PWN_RRDrvBultStuck_flg = false;
Boolean VbOUT_PWN_RLPsgBultStuck_flg = false;
Boolean VbOUT_PWN_ZCULFLWinInitializedSts_flg = false;
Boolean VbOUT_PWN_ZCULRLWinInitializedSts_flg = false;
Boolean VbOUT_PWN_DisablePassengerWndStsToEE_flg = false;
UInt8 VeOUT_PWN_PWNStatusResponse2ICM_sig = 0;
UInt8 VeOUT_PWN_PWNStatusResponse2TCP_sig = 0;
UInt8 VeOUT_PWN_PWNStatusResponse2DKM_sig = 0;
Boolean VbOUT_PWN_ZCULRainAutoClosedWndSts_flg = false;
Boolean VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg = false;
UInt8 VeOUT_PWN_ZCULFRWindowsControl_sig = 0;
UInt8 VeOUT_PWN_ZCULRRWindowsControl_sig = 0;
UInt8 VeOUT_PWN_ZCULFRWINCtrlCmd_sig = 0;
UInt8 VeOUT_PWN_ZCULRRWINCtrlCmd_sig = 0;
Boolean VbOUT_PWN_ALMWinUpError_flg = false;
Boolean VbOUT_PWN_AllWinCloseStsFb_flg = false;
UInt8 VeOUT_PWN_FLWinSrcHst_Array[10] = {0};
UInt8 VeOUT_PWN_RLWinSrcHst_Array[10] = {0};
Boolean VbOUT_PWN_FLWinOhpmode_flg = false;
Boolean VbOUT_PWN_RLWinOhpmode_flg = false;
Boolean VbOUT_PWN_FLForceWinCloseReq_flg = false;
Boolean VbOUT_PWN_RLForceWinCloseReq_flg = false;
UInt8 VeOUT_PWN_ZCULArmedCloseWndSts_sig = 0;
UInt8 VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig = 0;
Boolean VbOUT_PWN_ZCULWindowSts_flg = false;
UInt8 VeOUT_PWN_ZCULSourceCMDFR_sig = 0;
UInt8 VeOUT_PWN_ZCULSourceCMDRR_sig = 0;
UInt32 VnOUT_PWN_PWNFailReason2ICM_sig = 0;
UInt32 VnOUT_PWN_PWNFailReason2TCP_sig = 0;
UInt32 VnOUT_PWN_PWNFailReason2DKM_sig = 0;
UInt8 VeOUT_PWN_ZCULSourcebackFL_sig = 0;
UInt8 VeOUT_PWN_ZCULSourcebackRL_sig = 0;
Boolean VbOUT_PWN_SleepPermit_flg = false;
UInt8 VeINP_LIN_DSPFLWndSwSts_sig = 0;
UInt8 VeINP_LIN_DSPFRWndSwSts_sig = 0;
UInt8 VeINP_LIN_DSPRLWndSwSts_sig = 0;
UInt8 VeINP_LIN_DSPRRWndSwSts_sig = 0;
Boolean VbINP_HWA_RLPsngManUp_flg = false;
Boolean VbINP_HWA_RLPsngManDn_flg = false;
Boolean VbINP_HWA_RLPsngAutoUp_flg = false;
Boolean VbINP_HWA_RLPsngAutoDn_flg = false;
Boolean VbOUT_SP_ZCULFLDoorSts_flg = false;
Boolean VbOUT_SP_ZCULRLDoorSts_flg = false;
UInt8 VeINP_HWA_Voltage_100mV = 0;
UInt8 VeINP_HWA_FLFeedBackRunSts_sig = 0;
UInt8 VeINP_HWA_RLFeedBackRunSts_sig = 0;
UInt8 VeINP_HWA_FLStallSts_sig = 0;
UInt8 VeINP_HWA_RLStallSts_sig = 0;
UInt16 VuINP_HWA_FLWinPostion_sig = 0;
UInt16 VuINP_HWA_RLWinPostion_sig = 0;
Boolean VbINP_HWA_FLAntipinchEnable_flg = false;
Boolean VbINP_HWA_RLAntipinchEnable_flg = false;
UInt16 VuINP_HWA_FLWinPosMax_sig = 0;
UInt16 VuINP_HWA_RLWinPosMax_sig = 0;
Boolean VbINP_CAN_ICMDisablePassengerWndReq_flg = false;
Boolean VbINP_EPRM_DisablePassengerWndStsFromEE_flg = false;
Boolean VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg = false;
Boolean VbINP_CAN_VCCMFRDoorSts_flg = false;
Boolean VbINP_CAN_VCCMRRDoorSts_flg = false;
Boolean VbINP_CAN_VCCMTrunkDoorSts_flg = false;
UInt8 VeINP_CAN_ICMFLWindowsVoiControl_sig = 0;
UInt8 VeINP_CAN_ICMRLWindowsVoiControl_sig = 0;
UInt8 VeINP_CAN_ICMFRWindowsVoiControl_sig = 0;
UInt8 VeINP_CAN_ICMRRWindowsVoiControl_sig = 0;
UInt8 VeINP_CAN_TCPFLWindowsControl_sig = 0;
UInt8 VeINP_CAN_TCPRLWindowsControl_sig = 0;
UInt8 VeINP_CAN_TCPFRWindowsControl_sig = 0;
UInt8 VeINP_CAN_TCPRRWindowsControl_sig = 0;
UInt8 VeINP_CAN_DKMVCCMFLWindowsControl_sig = 0;
UInt8 VeINP_CAN_DKMVCCMRLWindowsControl_sig = 0;
UInt8 VeINP_CAN_DKMVCCMFRWindowsControl_sig = 0;
UInt8 VeINP_CAN_DKMVCCMRRWindowsControl_sig = 0;
UInt8 VeINP_CAN_ICMArmedCloseWndReq_sig = 0;
UInt8 VeINP_CAN_RLSWinCloseCmd_sig = 0;
Boolean VbINP_CAN_ICMRainAutoClosedWndReq_flg = false;
UInt8 VeOUT_PDU_ZCULSystemPowerSource_sig = 0;
Boolean VbINP_CAN_VCCMFRWinInitializedSts_flg = false;
Boolean VbINP_CAN_VCCMRRWinInitializedSts_flg = false;
UInt8 VeINP_CAN_VCCMFRWindowStatus_sig = 0;
UInt8 VeINP_CAN_VCCMRRWindowStatus_sig = 0;
UInt8 VeINP_CAN_VCCMFRWindowsMovSt_sig = 0;
UInt8 VeINP_CAN_VCCMRRWindowsMovSt_sig = 0;
Boolean VbINP_CAN_VCCMFRAntipinchSts_flg = false;
Boolean VbINP_CAN_VCCMRRAntipinchSts_flg = false;
UInt8 VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig = 0;
UInt8 VeINP_CAN_VCCMSourcebackFR_sig = 0;
UInt8 VeINP_CAN_VCCMSourcebackRR_sig = 0;
UInt8 VeINP_CAN_VCCMRunErrCauseFR_sig = 0;
UInt8 VeINP_CAN_VCCMRunErrCauseRR_sig = 0;
UInt8 VmOUT_ALM_AlarmState_enum = 0;
UInt8 VmOUT_ALM_PWNReq_enum = 0;
UInt8 VeINP_CAN_VCU1NActualGear_sig = 0;
UInt8 VeINP_HWA_FLWindowErrSource_sig = 0;
UInt8 VeINP_HWA_RLWindowErrSource_sig = 0;
UInt8 VeINP_CAN_ICMOTASts_sig = 0;
UInt8 VeOUT_SP_PowerMode_sig = 0;
UInt8 VeINP_HWA_SleepCommand_sig = 0;
UInt8 VeOUT_CMS_ZCULCarMode_sig = 0;
Boolean VbINP_BSW_EEReady_flg = false;
Boolean VbINP_HWA_FLPsngManUp_flg = false;
Boolean VbINP_HWA_FLPsngManDn_flg = false;
Boolean VbINP_HWA_FLPsngAutoUp_flg = false;
Boolean VbINP_HWA_FLPsngAutoDn_flg = false;
UInt8 VeINP_CAN_ICMWashModeSwSts_sig = 0;
Boolean VbINP_CAN_ICMCampingModeSwSts_flg = false;
Boolean VbINP_CAN_ICMOffVehPowerKeepSwSts_flg = false;
Boolean VbINP_CAN_PEPSRKElockSts_flg = false;
Boolean VbINP_CAN_PEPSRKEunlockSts_flg = false;
Boolean VbOUT_PDU_PowerModeValid_flg = false;
UInt8 VeOUT_ALM_ZCULAntiThelfSts_sig = 0;
Boolean VbINP_CAN_ICMInCarCampingSwSts_flg = false;
Boolean VbINP_CFG_LeRiRudder_flg = false;
Boolean VbINP_CAN_DKMWindowClose_flg = false;
Boolean VbINP_CAN_DKMWindowOpen_flg = false;
Boolean VbOUT_CMS_NAPModeSts_flg = false;

void TEST_RESET(void)
{
    memset((char*)&checkStruct,0,sizeof(checkStruct));
    printfCount = 0;
    memset((char*)&PWNL_B,0,sizeof(PWNL_B));
    memset((char*)&PWNL_DW,0,sizeof(PWNL_DW));

    VeOUT_PWN_ZCULFLWindowStatus_sig = 0;
    VeOUT_PWN_ZCULRLWindowStatus_sig = 0;
    VbOUT_PWN_ZCULDisablePassengerWndSts_flg = false;
    VmOUT_PWN_FLOutMovTypeState_enum = 0;
    VmOUT_PWN_RLOutMovTypeState_enum = 0;
    VmOUT_PWN_FLOutMotorState_enum = 0;
    VmOUT_PWN_RLOutMotorState_enum = 0;
    VbOUT_PWN_FLDrvBultStuck_flg = false;
    VbOUT_PWN_FRDrvBultStuck_flg = false;
    VbOUT_PWN_RLDrvBultStuck_flg = false;
    VbOUT_PWN_RRDrvBultStuck_flg = false;
    VbOUT_PWN_RLPsgBultStuck_flg = false;
    VbOUT_PWN_ZCULFLWinInitializedSts_flg = false;
    VbOUT_PWN_ZCULRLWinInitializedSts_flg = false;
    VbOUT_PWN_DisablePassengerWndStsToEE_flg = false;
    VeOUT_PWN_PWNStatusResponse2ICM_sig = 0;
    VeOUT_PWN_PWNStatusResponse2TCP_sig = 0;
    VeOUT_PWN_PWNStatusResponse2DKM_sig = 0;
    VbOUT_PWN_ZCULRainAutoClosedWndSts_flg = false;
    VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg = false;
    VeOUT_PWN_ZCULFRWindowsControl_sig = 0;
    VeOUT_PWN_ZCULRRWindowsControl_sig = 0;
    VeOUT_PWN_ZCULFRWINCtrlCmd_sig = 0;
    VeOUT_PWN_ZCULRRWINCtrlCmd_sig = 0;
    VbOUT_PWN_ALMWinUpError_flg = false;
    VbOUT_PWN_AllWinCloseStsFb_flg = false;
    VeOUT_PWN_FLWinSrcHst_Array[0] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[1] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[2] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[3] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[4] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[5] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[6] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[7] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[8] = 0;
    VeOUT_PWN_FLWinSrcHst_Array[9] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[0] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[1] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[2] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[3] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[4] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[5] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[6] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[7] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[8] = 0;
    VeOUT_PWN_RLWinSrcHst_Array[9] = 0;
    VbOUT_PWN_FLWinOhpmode_flg = false;
    VbOUT_PWN_RLWinOhpmode_flg = false;
    VbOUT_PWN_FLForceWinCloseReq_flg = false;
    VbOUT_PWN_RLForceWinCloseReq_flg = false;
    VeOUT_PWN_ZCULArmedCloseWndSts_sig = 0;
    VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig = 0;
    VbOUT_PWN_ZCULWindowSts_flg = false;
    VeOUT_PWN_ZCULSourceCMDFR_sig = 0;
    VeOUT_PWN_ZCULSourceCMDRR_sig = 0;
    VnOUT_PWN_PWNFailReason2ICM_sig = 0;
    VnOUT_PWN_PWNFailReason2TCP_sig = 0;
    VnOUT_PWN_PWNFailReason2DKM_sig = 0;
    VeOUT_PWN_ZCULSourcebackFL_sig = 0;
    VeOUT_PWN_ZCULSourcebackRL_sig = 0;
    VbOUT_PWN_SleepPermit_flg = false;
    VeINP_LIN_DSPFLWndSwSts_sig = 0;
    VeINP_LIN_DSPFRWndSwSts_sig = 0;
    VeINP_LIN_DSPRLWndSwSts_sig = 0;
    VeINP_LIN_DSPRRWndSwSts_sig = 0;
    VbINP_HWA_RLPsngManUp_flg = false;
    VbINP_HWA_RLPsngManDn_flg = false;
    VbINP_HWA_RLPsngAutoUp_flg = false;
    VbINP_HWA_RLPsngAutoDn_flg = false;
    VbOUT_SP_ZCULFLDoorSts_flg = false;
    VbOUT_SP_ZCULRLDoorSts_flg = false;
    VeINP_HWA_Voltage_100mV = 0;
    VeINP_HWA_FLFeedBackRunSts_sig = 0;
    VeINP_HWA_RLFeedBackRunSts_sig = 0;
    VeINP_HWA_FLStallSts_sig = 0;
    VeINP_HWA_RLStallSts_sig = 0;
    VuINP_HWA_FLWinPostion_sig = 0;
    VuINP_HWA_RLWinPostion_sig = 0;
    VbINP_HWA_FLAntipinchEnable_flg = false;
    VbINP_HWA_RLAntipinchEnable_flg = false;
    VuINP_HWA_FLWinPosMax_sig = 0;
    VuINP_HWA_RLWinPosMax_sig = 0;
    VbINP_CAN_ICMDisablePassengerWndReq_flg = false;
    VbINP_EPRM_DisablePassengerWndStsFromEE_flg = false;
    VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg = false;
    VbINP_CAN_VCCMFRDoorSts_flg = false;
    VbINP_CAN_VCCMRRDoorSts_flg = false;
    VbINP_CAN_VCCMTrunkDoorSts_flg = false;
    VeINP_CAN_ICMFLWindowsVoiControl_sig = 0;
    VeINP_CAN_ICMRLWindowsVoiControl_sig = 0;
    VeINP_CAN_ICMFRWindowsVoiControl_sig = 0;
    VeINP_CAN_ICMRRWindowsVoiControl_sig = 0;
    VeINP_CAN_TCPFLWindowsControl_sig = 0;
    VeINP_CAN_TCPRLWindowsControl_sig = 0;
    VeINP_CAN_TCPFRWindowsControl_sig = 0;
    VeINP_CAN_TCPRRWindowsControl_sig = 0;
    VeINP_CAN_DKMVCCMFLWindowsControl_sig = 0;
    VeINP_CAN_DKMVCCMRLWindowsControl_sig = 0;
    VeINP_CAN_DKMVCCMFRWindowsControl_sig = 0;
    VeINP_CAN_DKMVCCMRRWindowsControl_sig = 0;
    VeINP_CAN_ICMArmedCloseWndReq_sig = 0;
    VeINP_CAN_RLSWinCloseCmd_sig = 0;
    VbINP_CAN_ICMRainAutoClosedWndReq_flg = false;
    VeOUT_PDU_ZCULSystemPowerSource_sig = 0;
    VbINP_CAN_VCCMFRWinInitializedSts_flg = false;
    VbINP_CAN_VCCMRRWinInitializedSts_flg = false;
    VeINP_CAN_VCCMFRWindowStatus_sig = 0;
    VeINP_CAN_VCCMRRWindowStatus_sig = 0;
    VeINP_CAN_VCCMFRWindowsMovSt_sig = 0;
    VeINP_CAN_VCCMRRWindowsMovSt_sig = 0;
    VbINP_CAN_VCCMFRAntipinchSts_flg = false;
    VbINP_CAN_VCCMRRAntipinchSts_flg = false;
    VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig = 0;
    VeINP_CAN_VCCMSourcebackFR_sig = 0;
    VeINP_CAN_VCCMSourcebackRR_sig = 0;
    VeINP_CAN_VCCMRunErrCauseFR_sig = 0;
    VeINP_CAN_VCCMRunErrCauseRR_sig = 0;
    VmOUT_ALM_AlarmState_enum = 0;
    VmOUT_ALM_PWNReq_enum = 0;
    VeINP_CAN_VCU1NActualGear_sig = 0;
    VeINP_HWA_FLWindowErrSource_sig = 0;
    VeINP_HWA_RLWindowErrSource_sig = 0;
    VeINP_CAN_ICMOTASts_sig = 0;
    VeOUT_SP_PowerMode_sig = 0;
    VeINP_HWA_SleepCommand_sig = 0;
    VeOUT_CMS_ZCULCarMode_sig = 0;
    VbINP_BSW_EEReady_flg = false;
    VbINP_HWA_FLPsngManUp_flg = false;
    VbINP_HWA_FLPsngManDn_flg = false;
    VbINP_HWA_FLPsngAutoUp_flg = false;
    VbINP_HWA_FLPsngAutoDn_flg = false;
    VeINP_CAN_ICMWashModeSwSts_sig = 0;
    VbINP_CAN_ICMCampingModeSwSts_flg = false;
    VbINP_CAN_ICMOffVehPowerKeepSwSts_flg = false;
    VbINP_CAN_PEPSRKElockSts_flg = false;
    VbINP_CAN_PEPSRKEunlockSts_flg = false;
    VbOUT_PDU_PowerModeValid_flg = false;
    VeOUT_ALM_ZCULAntiThelfSts_sig = 0;
    VbINP_CAN_ICMInCarCampingSwSts_flg = false;
    VbINP_CFG_LeRiRudder_flg = false;
    VbINP_CAN_DKMWindowClose_flg = false;
    VbINP_CAN_DKMWindowOpen_flg = false;
    VbOUT_CMS_NAPModeSts_flg = false;
}



//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(UInt8 u)
{
    VeOUT_PWN_ZCULFLWindowStatus_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULFLWindowStatus_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFLWindowStatus_sig_VeOUT_PWN_ZCULFLWindowStatus_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(UInt8 u)
{
    VeOUT_PWN_ZCULRLWindowStatus_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULRLWindowStatus_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRLWindowStatus_sig_VeOUT_PWN_ZCULRLWindowStatus_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(Boolean u)
{
    VbOUT_PWN_ZCULDisablePassengerWndSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(Boolean* u)
{
    *u = VbOUT_PWN_ZCULDisablePassengerWndSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULDisablePassengerWndSts_flg_VbOUT_PWN_ZCULDisablePassengerWndSts_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(UInt8 u)
{
    VmOUT_PWN_FLOutMovTypeState_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(UInt8* u)
{
    *u = VmOUT_PWN_FLOutMovTypeState_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VmOUT_PWN_FLOutMovTypeState_enum_VmOUT_PWN_FLOutMovTypeState_enum(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(UInt8 u)
{
    VmOUT_PWN_RLOutMovTypeState_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(UInt8* u)
{
    *u = VmOUT_PWN_RLOutMovTypeState_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VmOUT_PWN_RLOutMovTypeState_enum_VmOUT_PWN_RLOutMovTypeState_enum(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(UInt8 u)
{
    VmOUT_PWN_FLOutMotorState_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(UInt8* u)
{
    *u = VmOUT_PWN_FLOutMotorState_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VmOUT_PWN_FLOutMotorState_enum_VmOUT_PWN_FLOutMotorState_enum(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(UInt8 u)
{
    VmOUT_PWN_RLOutMotorState_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(UInt8* u)
{
    *u = VmOUT_PWN_RLOutMotorState_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VmOUT_PWN_RLOutMotorState_enum_VmOUT_PWN_RLOutMotorState_enum(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(Boolean u)
{
    VbOUT_PWN_FLDrvBultStuck_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(Boolean* u)
{
    *u = VbOUT_PWN_FLDrvBultStuck_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLDrvBultStuck_flg_VbOUT_PWN_FLDrvBultStuck_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(Boolean u)
{
    VbOUT_PWN_FRDrvBultStuck_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(Boolean* u)
{
    *u = VbOUT_PWN_FRDrvBultStuck_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_FRDrvBultStuck_flg_VbOUT_PWN_FRDrvBultStuck_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(Boolean u)
{
    VbOUT_PWN_RLDrvBultStuck_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(Boolean* u)
{
    *u = VbOUT_PWN_RLDrvBultStuck_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLDrvBultStuck_flg_VbOUT_PWN_RLDrvBultStuck_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(Boolean u)
{
    VbOUT_PWN_RRDrvBultStuck_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(Boolean* u)
{
    *u = VbOUT_PWN_RRDrvBultStuck_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_RRDrvBultStuck_flg_VbOUT_PWN_RRDrvBultStuck_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(Boolean u)
{
    VbOUT_PWN_RLPsgBultStuck_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(Boolean* u)
{
    *u = VbOUT_PWN_RLPsgBultStuck_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLPsgBultStuck_flg_VbOUT_PWN_RLPsgBultStuck_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(Boolean u)
{
    VbOUT_PWN_ZCULFLWinInitializedSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(Boolean* u)
{
    *u = VbOUT_PWN_ZCULFLWinInitializedSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULFLWinInitializedSts_flg_VbOUT_PWN_ZCULFLWinInitializedSts_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(Boolean u)
{
    VbOUT_PWN_ZCULRLWinInitializedSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(Boolean* u)
{
    *u = VbOUT_PWN_ZCULRLWinInitializedSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULRLWinInitializedSts_flg_VbOUT_PWN_ZCULRLWinInitializedSts_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(Boolean u)
{
    VbOUT_PWN_DisablePassengerWndStsToEE_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(Boolean* u)
{
    *u = VbOUT_PWN_DisablePassengerWndStsToEE_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_DisablePassengerWndStsToEE_flg_VbOUT_PWN_DisablePassengerWndStsToEE_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(UInt8 u)
{
    VeOUT_PWN_PWNStatusResponse2ICM_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(UInt8* u)
{
    *u = VeOUT_PWN_PWNStatusResponse2ICM_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2ICM_sig_VeOUT_PWN_PWNStatusResponse2ICM_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(UInt8 u)
{
    VeOUT_PWN_PWNStatusResponse2TCP_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(UInt8* u)
{
    *u = VeOUT_PWN_PWNStatusResponse2TCP_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2TCP_sig_VeOUT_PWN_PWNStatusResponse2TCP_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(UInt8 u)
{
    VeOUT_PWN_PWNStatusResponse2DKM_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(UInt8* u)
{
    *u = VeOUT_PWN_PWNStatusResponse2DKM_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_PWNStatusResponse2DKM_sig_VeOUT_PWN_PWNStatusResponse2DKM_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(Boolean u)
{
    VbOUT_PWN_ZCULRainAutoClosedWndSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(Boolean* u)
{
    *u = VbOUT_PWN_ZCULRainAutoClosedWndSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_VbOUT_PWN_ZCULRainAutoClosedWndSts_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(Boolean u)
{
    VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(Boolean* u)
{
    *u = VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(UInt8 u)
{
    VeOUT_PWN_ZCULFRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULFRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFRWindowsControl_sig_VeOUT_PWN_ZCULFRWindowsControl_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(UInt8 u)
{
    VeOUT_PWN_ZCULRRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULRRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRRWindowsControl_sig_VeOUT_PWN_ZCULRRWindowsControl_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(UInt8 u)
{
    VeOUT_PWN_ZCULFRWINCtrlCmd_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULFRWINCtrlCmd_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULFRWINCtrlCmd_sig_VeOUT_PWN_ZCULFRWINCtrlCmd_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(UInt8 u)
{
    VeOUT_PWN_ZCULRRWINCtrlCmd_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULRRWINCtrlCmd_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULRRWINCtrlCmd_sig_VeOUT_PWN_ZCULRRWINCtrlCmd_sig(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(Boolean u)
{
    VbOUT_PWN_ALMWinUpError_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(Boolean* u)
{
    *u = VbOUT_PWN_ALMWinUpError_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ALMWinUpError_flg_VbOUT_PWN_ALMWinUpError_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(Boolean u)
{
    VbOUT_PWN_AllWinCloseStsFb_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(Boolean* u)
{
    *u = VbOUT_PWN_AllWinCloseStsFb_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Invalidate_VbOUT_PWN_AllWinCloseStsFb_flg_VbOUT_PWN_AllWinCloseStsFb_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_FLWinSrcHst_Array_VeOUT_PWN_FLWinSrcHst_Array(UInt8 * u)
{
    VeOUT_PWN_FLWinSrcHst_Array[0] = u[0];
    VeOUT_PWN_FLWinSrcHst_Array[1] = u[1];
    VeOUT_PWN_FLWinSrcHst_Array[2] = u[2];
    VeOUT_PWN_FLWinSrcHst_Array[3] = u[3];
    VeOUT_PWN_FLWinSrcHst_Array[4] = u[4];
    VeOUT_PWN_FLWinSrcHst_Array[5] = u[5];
    VeOUT_PWN_FLWinSrcHst_Array[6] = u[6];
    VeOUT_PWN_FLWinSrcHst_Array[7] = u[7];
    VeOUT_PWN_FLWinSrcHst_Array[8] = u[8];
    VeOUT_PWN_FLWinSrcHst_Array[9] = u[9];
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_FLWinSrcHst_Array_VeOUT_PWN_FLWinSrcHst_Array(UInt8 * u)
{
    u[0] = VeOUT_PWN_FLWinSrcHst_Array[0]; 
    u[1] = VeOUT_PWN_FLWinSrcHst_Array[1]; 
    u[2] = VeOUT_PWN_FLWinSrcHst_Array[2]; 
    u[3] = VeOUT_PWN_FLWinSrcHst_Array[3]; 
    u[4] = VeOUT_PWN_FLWinSrcHst_Array[4]; 
    u[5] = VeOUT_PWN_FLWinSrcHst_Array[5]; 
    u[6] = VeOUT_PWN_FLWinSrcHst_Array[6]; 
    u[7] = VeOUT_PWN_FLWinSrcHst_Array[7]; 
    u[8] = VeOUT_PWN_FLWinSrcHst_Array[8]; 
    u[9] = VeOUT_PWN_FLWinSrcHst_Array[9]; 
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_RLWinSrcHst_Array_VeOUT_PWN_RLWinSrcHst_Array(UInt8 * u)
{
    VeOUT_PWN_RLWinSrcHst_Array[0] = u[0];
    VeOUT_PWN_RLWinSrcHst_Array[1] = u[1];
    VeOUT_PWN_RLWinSrcHst_Array[2] = u[2];
    VeOUT_PWN_RLWinSrcHst_Array[3] = u[3];
    VeOUT_PWN_RLWinSrcHst_Array[4] = u[4];
    VeOUT_PWN_RLWinSrcHst_Array[5] = u[5];
    VeOUT_PWN_RLWinSrcHst_Array[6] = u[6];
    VeOUT_PWN_RLWinSrcHst_Array[7] = u[7];
    VeOUT_PWN_RLWinSrcHst_Array[8] = u[8];
    VeOUT_PWN_RLWinSrcHst_Array[9] = u[9];
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_RLWinSrcHst_Array_VeOUT_PWN_RLWinSrcHst_Array(UInt8 * u)
{
    u[0] = VeOUT_PWN_RLWinSrcHst_Array[0]; 
    u[1] = VeOUT_PWN_RLWinSrcHst_Array[1]; 
    u[2] = VeOUT_PWN_RLWinSrcHst_Array[2]; 
    u[3] = VeOUT_PWN_RLWinSrcHst_Array[3]; 
    u[4] = VeOUT_PWN_RLWinSrcHst_Array[4]; 
    u[5] = VeOUT_PWN_RLWinSrcHst_Array[5]; 
    u[6] = VeOUT_PWN_RLWinSrcHst_Array[6]; 
    u[7] = VeOUT_PWN_RLWinSrcHst_Array[7]; 
    u[8] = VeOUT_PWN_RLWinSrcHst_Array[8]; 
    u[9] = VeOUT_PWN_RLWinSrcHst_Array[9]; 
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(Boolean u)
{
    VbOUT_PWN_FLWinOhpmode_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(Boolean* u)
{
    *u = VbOUT_PWN_FLWinOhpmode_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLWinOhpmode_flg_VbOUT_PWN_FLWinOhpmode_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(Boolean u)
{
    VbOUT_PWN_RLWinOhpmode_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(Boolean* u)
{
    *u = VbOUT_PWN_RLWinOhpmode_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLWinOhpmode_flg_VbOUT_PWN_RLWinOhpmode_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(Boolean u)
{
    VbOUT_PWN_FLForceWinCloseReq_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(Boolean* u)
{
    *u = VbOUT_PWN_FLForceWinCloseReq_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_FLForceWinCloseReq_flg_VbOUT_PWN_FLForceWinCloseReq_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(Boolean u)
{
    VbOUT_PWN_RLForceWinCloseReq_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(Boolean* u)
{
    *u = VbOUT_PWN_RLForceWinCloseReq_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_RLForceWinCloseReq_flg_VbOUT_PWN_RLForceWinCloseReq_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(UInt8 u)
{
    VeOUT_PWN_ZCULArmedCloseWndSts_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULArmedCloseWndSts_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULArmedCloseWndSts_sig_VeOUT_PWN_ZCULArmedCloseWndSts_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(UInt8 u)
{
    VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(Boolean u)
{
    VbOUT_PWN_ZCULWindowSts_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(Boolean* u)
{
    *u = VbOUT_PWN_ZCULWindowSts_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_ZCULWindowSts_flg_VbOUT_PWN_ZCULWindowSts_flg(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(UInt8 u)
{
    VeOUT_PWN_ZCULSourceCMDFR_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULSourceCMDFR_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourceCMDFR_sig_VeOUT_PWN_ZCULSourceCMDFR_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(UInt8 u)
{
    VeOUT_PWN_ZCULSourceCMDRR_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULSourceCMDRR_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourceCMDRR_sig_VeOUT_PWN_ZCULSourceCMDRR_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(UInt32 u)
{
    VnOUT_PWN_PWNFailReason2ICM_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(UInt32* u)
{
    *u = VnOUT_PWN_PWNFailReason2ICM_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2ICM_sig_VnOUT_PWN_PWNFailReason2ICM_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(UInt32 u)
{
    VnOUT_PWN_PWNFailReason2TCP_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(UInt32* u)
{
    *u = VnOUT_PWN_PWNFailReason2TCP_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2TCP_sig_VnOUT_PWN_PWNFailReason2TCP_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(UInt32 u)
{
    VnOUT_PWN_PWNFailReason2DKM_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(UInt32* u)
{
    *u = VnOUT_PWN_PWNFailReason2DKM_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VnOUT_PWN_PWNFailReason2DKM_sig_VnOUT_PWN_PWNFailReason2DKM_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(UInt8 u)
{
    VeOUT_PWN_ZCULSourcebackFL_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULSourcebackFL_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourcebackFL_sig_VeOUT_PWN_ZCULSourcebackFL_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(UInt8 u)
{
    VeOUT_PWN_ZCULSourcebackRL_sig = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(UInt8* u)
{
    *u = VeOUT_PWN_ZCULSourcebackRL_sig;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VeOUT_PWN_ZCULSourcebackRL_sig_VeOUT_PWN_ZCULSourcebackRL_sig(void)
{
    return true;
}

//UInt8

Std_ReturnType Rte_Write_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(Boolean u)
{
    VbOUT_PWN_SleepPermit_flg = u;
    return true;
}

//UInt8

Std_ReturnType Rte_Read_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(Boolean* u)
{
    *u = VbOUT_PWN_SleepPermit_flg;
    return true;
}

//UInt8

Std_ReturnType Rte_Invalidate_VbOUT_PWN_SleepPermit_flg_VbOUT_PWN_SleepPermit_flg(void)
{
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_LIN_DSPFLWndSwSts_sig_VeINP_LIN_DSPFLWndSwSts_sig(UInt8 u)
{
    VeINP_LIN_DSPFLWndSwSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_LIN_DSPFLWndSwSts_sig_VeINP_LIN_DSPFLWndSwSts_sig(UInt8* u)
{
    *u = VeINP_LIN_DSPFLWndSwSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_LIN_DSPFRWndSwSts_sig_VeINP_LIN_DSPFRWndSwSts_sig(UInt8 u)
{
    VeINP_LIN_DSPFRWndSwSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_LIN_DSPFRWndSwSts_sig_VeINP_LIN_DSPFRWndSwSts_sig(UInt8* u)
{
    *u = VeINP_LIN_DSPFRWndSwSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_LIN_DSPRLWndSwSts_sig_VeINP_LIN_DSPRLWndSwSts_sig(UInt8 u)
{
    VeINP_LIN_DSPRLWndSwSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_LIN_DSPRLWndSwSts_sig_VeINP_LIN_DSPRLWndSwSts_sig(UInt8* u)
{
    *u = VeINP_LIN_DSPRLWndSwSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_LIN_DSPRRWndSwSts_sig_VeINP_LIN_DSPRRWndSwSts_sig(UInt8 u)
{
    VeINP_LIN_DSPRRWndSwSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_LIN_DSPRRWndSwSts_sig_VeINP_LIN_DSPRRWndSwSts_sig(UInt8* u)
{
    *u = VeINP_LIN_DSPRRWndSwSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_RLPsngManUp_flg_VbINP_HWA_RLPsngManUp_flg(Boolean u)
{
    VbINP_HWA_RLPsngManUp_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_RLPsngManUp_flg_VbINP_HWA_RLPsngManUp_flg(Boolean* u)
{
    *u = VbINP_HWA_RLPsngManUp_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_RLPsngManDn_flg_VbINP_HWA_RLPsngManDn_flg(Boolean u)
{
    VbINP_HWA_RLPsngManDn_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_RLPsngManDn_flg_VbINP_HWA_RLPsngManDn_flg(Boolean* u)
{
    *u = VbINP_HWA_RLPsngManDn_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_RLPsngAutoUp_flg_VbINP_HWA_RLPsngAutoUp_flg(Boolean u)
{
    VbINP_HWA_RLPsngAutoUp_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_RLPsngAutoUp_flg_VbINP_HWA_RLPsngAutoUp_flg(Boolean* u)
{
    *u = VbINP_HWA_RLPsngAutoUp_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_RLPsngAutoDn_flg_VbINP_HWA_RLPsngAutoDn_flg(Boolean u)
{
    VbINP_HWA_RLPsngAutoDn_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_RLPsngAutoDn_flg_VbINP_HWA_RLPsngAutoDn_flg(Boolean* u)
{
    *u = VbINP_HWA_RLPsngAutoDn_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_SP_ZCULFLDoorSts_flg_VbOUT_SP_ZCULFLDoorSts_flg(Boolean u)
{
    VbOUT_SP_ZCULFLDoorSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_SP_ZCULFLDoorSts_flg_VbOUT_SP_ZCULFLDoorSts_flg(Boolean* u)
{
    *u = VbOUT_SP_ZCULFLDoorSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_SP_ZCULRLDoorSts_flg_VbOUT_SP_ZCULRLDoorSts_flg(Boolean u)
{
    VbOUT_SP_ZCULRLDoorSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_SP_ZCULRLDoorSts_flg_VbOUT_SP_ZCULRLDoorSts_flg(Boolean* u)
{
    *u = VbOUT_SP_ZCULRLDoorSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_Voltage_100mV_VeINP_HWA_Voltage_100mV(UInt8 u)
{
    VeINP_HWA_Voltage_100mV = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_Voltage_100mV_VeINP_HWA_Voltage_100mV(UInt8* u)
{
    *u = VeINP_HWA_Voltage_100mV;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_FLFeedBackRunSts_sig_VeINP_HWA_FLFeedBackRunSts_sig(UInt8 u)
{
    VeINP_HWA_FLFeedBackRunSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_FLFeedBackRunSts_sig_VeINP_HWA_FLFeedBackRunSts_sig(UInt8* u)
{
    *u = VeINP_HWA_FLFeedBackRunSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_RLFeedBackRunSts_sig_VeINP_HWA_RLFeedBackRunSts_sig(UInt8 u)
{
    VeINP_HWA_RLFeedBackRunSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_RLFeedBackRunSts_sig_VeINP_HWA_RLFeedBackRunSts_sig(UInt8* u)
{
    *u = VeINP_HWA_RLFeedBackRunSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_FLStallSts_sig_VeINP_HWA_FLStallSts_sig(UInt8 u)
{
    VeINP_HWA_FLStallSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_FLStallSts_sig_VeINP_HWA_FLStallSts_sig(UInt8* u)
{
    *u = VeINP_HWA_FLStallSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_RLStallSts_sig_VeINP_HWA_RLStallSts_sig(UInt8 u)
{
    VeINP_HWA_RLStallSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_RLStallSts_sig_VeINP_HWA_RLStallSts_sig(UInt8* u)
{
    *u = VeINP_HWA_RLStallSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VuINP_HWA_FLWinPostion_sig_VuINP_HWA_FLWinPostion_sig(UInt16 u)
{
    VuINP_HWA_FLWinPostion_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VuINP_HWA_FLWinPostion_sig_VuINP_HWA_FLWinPostion_sig(UInt16* u)
{
    *u = VuINP_HWA_FLWinPostion_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VuINP_HWA_RLWinPostion_sig_VuINP_HWA_RLWinPostion_sig(UInt16 u)
{
    VuINP_HWA_RLWinPostion_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VuINP_HWA_RLWinPostion_sig_VuINP_HWA_RLWinPostion_sig(UInt16* u)
{
    *u = VuINP_HWA_RLWinPostion_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_FLAntipinchEnable_flg_VbINP_HWA_FLAntipinchEnable_flg(Boolean u)
{
    VbINP_HWA_FLAntipinchEnable_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_FLAntipinchEnable_flg_VbINP_HWA_FLAntipinchEnable_flg(Boolean* u)
{
    *u = VbINP_HWA_FLAntipinchEnable_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_RLAntipinchEnable_flg_VbINP_HWA_RLAntipinchEnable_flg(Boolean u)
{
    VbINP_HWA_RLAntipinchEnable_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_RLAntipinchEnable_flg_VbINP_HWA_RLAntipinchEnable_flg(Boolean* u)
{
    *u = VbINP_HWA_RLAntipinchEnable_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VuINP_HWA_FLWinPosMax_sig_VuINP_HWA_FLWinPosMax_sig(UInt16 u)
{
    VuINP_HWA_FLWinPosMax_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VuINP_HWA_FLWinPosMax_sig_VuINP_HWA_FLWinPosMax_sig(UInt16* u)
{
    *u = VuINP_HWA_FLWinPosMax_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VuINP_HWA_RLWinPosMax_sig_VuINP_HWA_RLWinPosMax_sig(UInt16 u)
{
    VuINP_HWA_RLWinPosMax_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VuINP_HWA_RLWinPosMax_sig_VuINP_HWA_RLWinPosMax_sig(UInt16* u)
{
    *u = VuINP_HWA_RLWinPosMax_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_ICMDisablePassengerWndReq_flg_VbINP_CAN_ICMDisablePassengerWndReq_flg(Boolean u)
{
    VbINP_CAN_ICMDisablePassengerWndReq_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_ICMDisablePassengerWndReq_flg_VbINP_CAN_ICMDisablePassengerWndReq_flg(Boolean* u)
{
    *u = VbINP_CAN_ICMDisablePassengerWndReq_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_EPRM_DisablePassengerWndStsFromEE_flg_VbINP_EPRM_DisablePassengerWndStsFromEE_flg(Boolean u)
{
    VbINP_EPRM_DisablePassengerWndStsFromEE_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_EPRM_DisablePassengerWndStsFromEE_flg_VbINP_EPRM_DisablePassengerWndStsFromEE_flg(Boolean* u)
{
    *u = VbINP_EPRM_DisablePassengerWndStsFromEE_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg(Boolean u)
{
    VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg(Boolean* u)
{
    *u = VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRDoorSts_flg_VbINP_CAN_VCCMFRDoorSts_flg(Boolean u)
{
    VbINP_CAN_VCCMFRDoorSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRDoorSts_flg_VbINP_CAN_VCCMFRDoorSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMFRDoorSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRDoorSts_flg_VbINP_CAN_VCCMRRDoorSts_flg(Boolean u)
{
    VbINP_CAN_VCCMRRDoorSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRDoorSts_flg_VbINP_CAN_VCCMRRDoorSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMRRDoorSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMTrunkDoorSts_flg_VbINP_CAN_VCCMTrunkDoorSts_flg(Boolean u)
{
    VbINP_CAN_VCCMTrunkDoorSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMTrunkDoorSts_flg_VbINP_CAN_VCCMTrunkDoorSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMTrunkDoorSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMFLWindowsVoiControl_sig_VeINP_CAN_ICMFLWindowsVoiControl_sig(UInt8 u)
{
    VeINP_CAN_ICMFLWindowsVoiControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMFLWindowsVoiControl_sig_VeINP_CAN_ICMFLWindowsVoiControl_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMFLWindowsVoiControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMRLWindowsVoiControl_sig_VeINP_CAN_ICMRLWindowsVoiControl_sig(UInt8 u)
{
    VeINP_CAN_ICMRLWindowsVoiControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMRLWindowsVoiControl_sig_VeINP_CAN_ICMRLWindowsVoiControl_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMRLWindowsVoiControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMFRWindowsVoiControl_sig_VeINP_CAN_ICMFRWindowsVoiControl_sig(UInt8 u)
{
    VeINP_CAN_ICMFRWindowsVoiControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMFRWindowsVoiControl_sig_VeINP_CAN_ICMFRWindowsVoiControl_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMFRWindowsVoiControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMRRWindowsVoiControl_sig_VeINP_CAN_ICMRRWindowsVoiControl_sig(UInt8 u)
{
    VeINP_CAN_ICMRRWindowsVoiControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMRRWindowsVoiControl_sig_VeINP_CAN_ICMRRWindowsVoiControl_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMRRWindowsVoiControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_TCPFLWindowsControl_sig_VeINP_CAN_TCPFLWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_TCPFLWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_TCPFLWindowsControl_sig_VeINP_CAN_TCPFLWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_TCPFLWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_TCPRLWindowsControl_sig_VeINP_CAN_TCPRLWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_TCPRLWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_TCPRLWindowsControl_sig_VeINP_CAN_TCPRLWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_TCPRLWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_TCPFRWindowsControl_sig_VeINP_CAN_TCPFRWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_TCPFRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_TCPFRWindowsControl_sig_VeINP_CAN_TCPFRWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_TCPFRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_TCPRRWindowsControl_sig_VeINP_CAN_TCPRRWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_TCPRRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_TCPRRWindowsControl_sig_VeINP_CAN_TCPRRWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_TCPRRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMFLWindowsControl_sig_VeINP_CAN_DKMVCCMFLWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_DKMVCCMFLWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMFLWindowsControl_sig_VeINP_CAN_DKMVCCMFLWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_DKMVCCMFLWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMRLWindowsControl_sig_VeINP_CAN_DKMVCCMRLWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_DKMVCCMRLWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMRLWindowsControl_sig_VeINP_CAN_DKMVCCMRLWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_DKMVCCMRLWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMFRWindowsControl_sig_VeINP_CAN_DKMVCCMFRWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_DKMVCCMFRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMFRWindowsControl_sig_VeINP_CAN_DKMVCCMFRWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_DKMVCCMFRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_DKMVCCMRRWindowsControl_sig_VeINP_CAN_DKMVCCMRRWindowsControl_sig(UInt8 u)
{
    VeINP_CAN_DKMVCCMRRWindowsControl_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_DKMVCCMRRWindowsControl_sig_VeINP_CAN_DKMVCCMRRWindowsControl_sig(UInt8* u)
{
    *u = VeINP_CAN_DKMVCCMRRWindowsControl_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMArmedCloseWndReq_sig_VeINP_CAN_ICMArmedCloseWndReq_sig(UInt8 u)
{
    VeINP_CAN_ICMArmedCloseWndReq_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMArmedCloseWndReq_sig_VeINP_CAN_ICMArmedCloseWndReq_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMArmedCloseWndReq_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_RLSWinCloseCmd_sig_VeINP_CAN_RLSWinCloseCmd_sig(UInt8 u)
{
    VeINP_CAN_RLSWinCloseCmd_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_RLSWinCloseCmd_sig_VeINP_CAN_RLSWinCloseCmd_sig(UInt8* u)
{
    *u = VeINP_CAN_RLSWinCloseCmd_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_ICMRainAutoClosedWndReq_flg_VbINP_CAN_ICMRainAutoClosedWndReq_flg(Boolean u)
{
    VbINP_CAN_ICMRainAutoClosedWndReq_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_ICMRainAutoClosedWndReq_flg_VbINP_CAN_ICMRainAutoClosedWndReq_flg(Boolean* u)
{
    *u = VbINP_CAN_ICMRainAutoClosedWndReq_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_PDU_ZCULSystemPowerSource_sig_VeOUT_PDU_ZCULSystemPowerSource_sig(UInt8 u)
{
    VeOUT_PDU_ZCULSystemPowerSource_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_PDU_ZCULSystemPowerSource_sig_VeOUT_PDU_ZCULSystemPowerSource_sig(UInt8* u)
{
    *u = VeOUT_PDU_ZCULSystemPowerSource_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRWinInitializedSts_flg_VbINP_CAN_VCCMFRWinInitializedSts_flg(Boolean u)
{
    VbINP_CAN_VCCMFRWinInitializedSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRWinInitializedSts_flg_VbINP_CAN_VCCMFRWinInitializedSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMFRWinInitializedSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRWinInitializedSts_flg_VbINP_CAN_VCCMRRWinInitializedSts_flg(Boolean u)
{
    VbINP_CAN_VCCMRRWinInitializedSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRWinInitializedSts_flg_VbINP_CAN_VCCMRRWinInitializedSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMRRWinInitializedSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMFRWindowStatus_sig_VeINP_CAN_VCCMFRWindowStatus_sig(UInt8 u)
{
    VeINP_CAN_VCCMFRWindowStatus_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMFRWindowStatus_sig_VeINP_CAN_VCCMFRWindowStatus_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMFRWindowStatus_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMRRWindowStatus_sig_VeINP_CAN_VCCMRRWindowStatus_sig(UInt8 u)
{
    VeINP_CAN_VCCMRRWindowStatus_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMRRWindowStatus_sig_VeINP_CAN_VCCMRRWindowStatus_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMRRWindowStatus_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMFRWindowsMovSt_sig_VeINP_CAN_VCCMFRWindowsMovSt_sig(UInt8 u)
{
    VeINP_CAN_VCCMFRWindowsMovSt_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMFRWindowsMovSt_sig_VeINP_CAN_VCCMFRWindowsMovSt_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMFRWindowsMovSt_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMRRWindowsMovSt_sig_VeINP_CAN_VCCMRRWindowsMovSt_sig(UInt8 u)
{
    VeINP_CAN_VCCMRRWindowsMovSt_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMRRWindowsMovSt_sig_VeINP_CAN_VCCMRRWindowsMovSt_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMRRWindowsMovSt_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMFRAntipinchSts_flg_VbINP_CAN_VCCMFRAntipinchSts_flg(Boolean u)
{
    VbINP_CAN_VCCMFRAntipinchSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMFRAntipinchSts_flg_VbINP_CAN_VCCMFRAntipinchSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMFRAntipinchSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_VCCMRRAntipinchSts_flg_VbINP_CAN_VCCMRRAntipinchSts_flg(Boolean u)
{
    VbINP_CAN_VCCMRRAntipinchSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_VCCMRRAntipinchSts_flg_VbINP_CAN_VCCMRRAntipinchSts_flg(Boolean* u)
{
    *u = VbINP_CAN_VCCMRRAntipinchSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig(UInt8 u)
{
    VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig(UInt8* u)
{
    *u = VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMSourcebackFR_sig_VeINP_CAN_VCCMSourcebackFR_sig(UInt8 u)
{
    VeINP_CAN_VCCMSourcebackFR_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMSourcebackFR_sig_VeINP_CAN_VCCMSourcebackFR_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMSourcebackFR_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMSourcebackRR_sig_VeINP_CAN_VCCMSourcebackRR_sig(UInt8 u)
{
    VeINP_CAN_VCCMSourcebackRR_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMSourcebackRR_sig_VeINP_CAN_VCCMSourcebackRR_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMSourcebackRR_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMRunErrCauseFR_sig_VeINP_CAN_VCCMRunErrCauseFR_sig(UInt8 u)
{
    VeINP_CAN_VCCMRunErrCauseFR_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMRunErrCauseFR_sig_VeINP_CAN_VCCMRunErrCauseFR_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMRunErrCauseFR_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCCMRunErrCauseRR_sig_VeINP_CAN_VCCMRunErrCauseRR_sig(UInt8 u)
{
    VeINP_CAN_VCCMRunErrCauseRR_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCCMRunErrCauseRR_sig_VeINP_CAN_VCCMRunErrCauseRR_sig(UInt8* u)
{
    *u = VeINP_CAN_VCCMRunErrCauseRR_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_ALM_AlarmState_enum_VmOUT_ALM_AlarmState_enum(UInt8 u)
{
    VmOUT_ALM_AlarmState_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_ALM_AlarmState_enum_VmOUT_ALM_AlarmState_enum(UInt8* u)
{
    *u = VmOUT_ALM_AlarmState_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VmOUT_ALM_PWNReq_enum_VmOUT_ALM_PWNReq_enum(UInt8 u)
{
    VmOUT_ALM_PWNReq_enum = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VmOUT_ALM_PWNReq_enum_VmOUT_ALM_PWNReq_enum(UInt8* u)
{
    *u = VmOUT_ALM_PWNReq_enum;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_VCU1NActualGear_sig_VeINP_CAN_VCU1NActualGear_sig(UInt8 u)
{
    VeINP_CAN_VCU1NActualGear_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_VCU1NActualGear_sig_VeINP_CAN_VCU1NActualGear_sig(UInt8* u)
{
    *u = VeINP_CAN_VCU1NActualGear_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_FLWindowErrSource_sig_VeINP_HWA_FLWindowErrSource_sig(UInt8 u)
{
    VeINP_HWA_FLWindowErrSource_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_FLWindowErrSource_sig_VeINP_HWA_FLWindowErrSource_sig(UInt8* u)
{
    *u = VeINP_HWA_FLWindowErrSource_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_RLWindowErrSource_sig_VeINP_HWA_RLWindowErrSource_sig(UInt8 u)
{
    VeINP_HWA_RLWindowErrSource_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_RLWindowErrSource_sig_VeINP_HWA_RLWindowErrSource_sig(UInt8* u)
{
    *u = VeINP_HWA_RLWindowErrSource_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMOTASts_sig_VeINP_CAN_ICMOTASts_sig(UInt8 u)
{
    VeINP_CAN_ICMOTASts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMOTASts_sig_VeINP_CAN_ICMOTASts_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMOTASts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_SP_PowerMode_sig_VeOUT_SP_PowerMode_sig(UInt8 u)
{
    VeOUT_SP_PowerMode_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_SP_PowerMode_sig_VeOUT_SP_PowerMode_sig(UInt8* u)
{
    *u = VeOUT_SP_PowerMode_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_HWA_SleepCommand_sig_VeINP_HWA_SleepCommand_sig(UInt8 u)
{
    VeINP_HWA_SleepCommand_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_HWA_SleepCommand_sig_VeINP_HWA_SleepCommand_sig(UInt8* u)
{
    *u = VeINP_HWA_SleepCommand_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_CMS_ZCULCarMode_sig_VeOUT_CMS_ZCULCarMode_sig(UInt8 u)
{
    VeOUT_CMS_ZCULCarMode_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_CMS_ZCULCarMode_sig_VeOUT_CMS_ZCULCarMode_sig(UInt8* u)
{
    *u = VeOUT_CMS_ZCULCarMode_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_BSW_EEReady_flg_VbINP_BSW_EEReady_flg(Boolean u)
{
    VbINP_BSW_EEReady_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_BSW_EEReady_flg_VbINP_BSW_EEReady_flg(Boolean* u)
{
    *u = VbINP_BSW_EEReady_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_FLPsngManUp_flg_VbINP_HWA_FLPsngManUp_flg(Boolean u)
{
    VbINP_HWA_FLPsngManUp_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_FLPsngManUp_flg_VbINP_HWA_FLPsngManUp_flg(Boolean* u)
{
    *u = VbINP_HWA_FLPsngManUp_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_FLPsngManDn_flg_VbINP_HWA_FLPsngManDn_flg(Boolean u)
{
    VbINP_HWA_FLPsngManDn_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_FLPsngManDn_flg_VbINP_HWA_FLPsngManDn_flg(Boolean* u)
{
    *u = VbINP_HWA_FLPsngManDn_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_FLPsngAutoUp_flg_VbINP_HWA_FLPsngAutoUp_flg(Boolean u)
{
    VbINP_HWA_FLPsngAutoUp_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_FLPsngAutoUp_flg_VbINP_HWA_FLPsngAutoUp_flg(Boolean* u)
{
    *u = VbINP_HWA_FLPsngAutoUp_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_HWA_FLPsngAutoDn_flg_VbINP_HWA_FLPsngAutoDn_flg(Boolean u)
{
    VbINP_HWA_FLPsngAutoDn_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_HWA_FLPsngAutoDn_flg_VbINP_HWA_FLPsngAutoDn_flg(Boolean* u)
{
    *u = VbINP_HWA_FLPsngAutoDn_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeINP_CAN_ICMWashModeSwSts_sig_VeINP_CAN_ICMWashModeSwSts_sig(UInt8 u)
{
    VeINP_CAN_ICMWashModeSwSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeINP_CAN_ICMWashModeSwSts_sig_VeINP_CAN_ICMWashModeSwSts_sig(UInt8* u)
{
    *u = VeINP_CAN_ICMWashModeSwSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_ICMCampingModeSwSts_flg_VbINP_CAN_ICMCampingModeSwSts_flg(Boolean u)
{
    VbINP_CAN_ICMCampingModeSwSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_ICMCampingModeSwSts_flg_VbINP_CAN_ICMCampingModeSwSts_flg(Boolean* u)
{
    *u = VbINP_CAN_ICMCampingModeSwSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg(Boolean u)
{
    VbINP_CAN_ICMOffVehPowerKeepSwSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_VbINP_CAN_ICMOffVehPowerKeepSwSts_flg(Boolean* u)
{
    *u = VbINP_CAN_ICMOffVehPowerKeepSwSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_PEPSRKElockSts_flg_VbINP_CAN_PEPSRKElockSts_flg(Boolean u)
{
    VbINP_CAN_PEPSRKElockSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_PEPSRKElockSts_flg_VbINP_CAN_PEPSRKElockSts_flg(Boolean* u)
{
    *u = VbINP_CAN_PEPSRKElockSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_PEPSRKEunlockSts_flg_VbINP_CAN_PEPSRKEunlockSts_flg(Boolean u)
{
    VbINP_CAN_PEPSRKEunlockSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_PEPSRKEunlockSts_flg_VbINP_CAN_PEPSRKEunlockSts_flg(Boolean* u)
{
    *u = VbINP_CAN_PEPSRKEunlockSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_PDU_PowerModeValid_flg_VbOUT_PDU_PowerModeValid_flg(Boolean u)
{
    VbOUT_PDU_PowerModeValid_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_PDU_PowerModeValid_flg_VbOUT_PDU_PowerModeValid_flg(Boolean* u)
{
    *u = VbOUT_PDU_PowerModeValid_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VeOUT_ALM_ZCULAntiThelfSts_sig_VeOUT_ALM_ZCULAntiThelfSts_sig(UInt8 u)
{
    VeOUT_ALM_ZCULAntiThelfSts_sig = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VeOUT_ALM_ZCULAntiThelfSts_sig_VeOUT_ALM_ZCULAntiThelfSts_sig(UInt8* u)
{
    *u = VeOUT_ALM_ZCULAntiThelfSts_sig;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_ICMInCarCampingSwSts_flg_VbINP_CAN_ICMInCarCampingSwSts_flg(Boolean u)
{
    VbINP_CAN_ICMInCarCampingSwSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_ICMInCarCampingSwSts_flg_VbINP_CAN_ICMInCarCampingSwSts_flg(Boolean* u)
{
    *u = VbINP_CAN_ICMInCarCampingSwSts_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CFG_LeRiRudder_flg_VbINP_CFG_LeRiRudder_flg(Boolean u)
{
    VbINP_CFG_LeRiRudder_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CFG_LeRiRudder_flg_VbINP_CFG_LeRiRudder_flg(Boolean* u)
{
    *u = VbINP_CFG_LeRiRudder_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_DKMWindowClose_flg_VbINP_CAN_DKMWindowClose_flg(Boolean u)
{
    VbINP_CAN_DKMWindowClose_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_DKMWindowClose_flg_VbINP_CAN_DKMWindowClose_flg(Boolean* u)
{
    *u = VbINP_CAN_DKMWindowClose_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbINP_CAN_DKMWindowOpen_flg_VbINP_CAN_DKMWindowOpen_flg(Boolean u)
{
    VbINP_CAN_DKMWindowOpen_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbINP_CAN_DKMWindowOpen_flg_VbINP_CAN_DKMWindowOpen_flg(Boolean* u)
{
    *u = VbINP_CAN_DKMWindowOpen_flg;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Write_VbOUT_CMS_NAPModeSts_flg_VbOUT_CMS_NAPModeSts_flg(Boolean u)
{
    VbOUT_CMS_NAPModeSts_flg = u;
    return true;
}

//PWN.arxml

Std_ReturnType Rte_Read_VbOUT_CMS_NAPModeSts_flg_VbOUT_CMS_NAPModeSts_flg(Boolean* u)
{
    *u = VbOUT_CMS_NAPModeSts_flg;
    return true;
}

/* IRV functions */

void setDebugTxt(UInt8 cycle) {

  if (fp_output)
  {
      if (VeOUT_PWN_ZCULFLWindowStatus_sig != checkStruct.VeOUT_PWN_ZCULFLWindowStatus_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULFLWindowStatus_sig %d %f\n", VeOUT_PWN_ZCULFLWindowStatus_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULFLWindowStatus_sig_old = VeOUT_PWN_ZCULFLWindowStatus_sig;
      }
      if (VeOUT_PWN_ZCULRLWindowStatus_sig != checkStruct.VeOUT_PWN_ZCULRLWindowStatus_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULRLWindowStatus_sig %d %f\n", VeOUT_PWN_ZCULRLWindowStatus_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULRLWindowStatus_sig_old = VeOUT_PWN_ZCULRLWindowStatus_sig;
      }
      if (VbOUT_PWN_ZCULDisablePassengerWndSts_flg != checkStruct.VbOUT_PWN_ZCULDisablePassengerWndSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ZCULDisablePassengerWndSts_flg %d %f\n", VbOUT_PWN_ZCULDisablePassengerWndSts_flg, Timestamp);
          checkStruct.VbOUT_PWN_ZCULDisablePassengerWndSts_flg_old = VbOUT_PWN_ZCULDisablePassengerWndSts_flg;
      }
      if (VmOUT_PWN_FLOutMovTypeState_enum != checkStruct.VmOUT_PWN_FLOutMovTypeState_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_PWN_FLOutMovTypeState_enum %d %f\n", VmOUT_PWN_FLOutMovTypeState_enum, Timestamp);
          checkStruct.VmOUT_PWN_FLOutMovTypeState_enum_old = VmOUT_PWN_FLOutMovTypeState_enum;
      }
      if (VmOUT_PWN_RLOutMovTypeState_enum != checkStruct.VmOUT_PWN_RLOutMovTypeState_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_PWN_RLOutMovTypeState_enum %d %f\n", VmOUT_PWN_RLOutMovTypeState_enum, Timestamp);
          checkStruct.VmOUT_PWN_RLOutMovTypeState_enum_old = VmOUT_PWN_RLOutMovTypeState_enum;
      }
      if (VmOUT_PWN_FLOutMotorState_enum != checkStruct.VmOUT_PWN_FLOutMotorState_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_PWN_FLOutMotorState_enum %d %f\n", VmOUT_PWN_FLOutMotorState_enum, Timestamp);
          checkStruct.VmOUT_PWN_FLOutMotorState_enum_old = VmOUT_PWN_FLOutMotorState_enum;
      }
      if (VmOUT_PWN_RLOutMotorState_enum != checkStruct.VmOUT_PWN_RLOutMotorState_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_PWN_RLOutMotorState_enum %d %f\n", VmOUT_PWN_RLOutMotorState_enum, Timestamp);
          checkStruct.VmOUT_PWN_RLOutMotorState_enum_old = VmOUT_PWN_RLOutMotorState_enum;
      }
      if (VbOUT_PWN_FLDrvBultStuck_flg != checkStruct.VbOUT_PWN_FLDrvBultStuck_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_FLDrvBultStuck_flg %d %f\n", VbOUT_PWN_FLDrvBultStuck_flg, Timestamp);
          checkStruct.VbOUT_PWN_FLDrvBultStuck_flg_old = VbOUT_PWN_FLDrvBultStuck_flg;
      }
      if (VbOUT_PWN_FRDrvBultStuck_flg != checkStruct.VbOUT_PWN_FRDrvBultStuck_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_FRDrvBultStuck_flg %d %f\n", VbOUT_PWN_FRDrvBultStuck_flg, Timestamp);
          checkStruct.VbOUT_PWN_FRDrvBultStuck_flg_old = VbOUT_PWN_FRDrvBultStuck_flg;
      }
      if (VbOUT_PWN_RLDrvBultStuck_flg != checkStruct.VbOUT_PWN_RLDrvBultStuck_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_RLDrvBultStuck_flg %d %f\n", VbOUT_PWN_RLDrvBultStuck_flg, Timestamp);
          checkStruct.VbOUT_PWN_RLDrvBultStuck_flg_old = VbOUT_PWN_RLDrvBultStuck_flg;
      }
      if (VbOUT_PWN_RRDrvBultStuck_flg != checkStruct.VbOUT_PWN_RRDrvBultStuck_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_RRDrvBultStuck_flg %d %f\n", VbOUT_PWN_RRDrvBultStuck_flg, Timestamp);
          checkStruct.VbOUT_PWN_RRDrvBultStuck_flg_old = VbOUT_PWN_RRDrvBultStuck_flg;
      }
      if (VbOUT_PWN_RLPsgBultStuck_flg != checkStruct.VbOUT_PWN_RLPsgBultStuck_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_RLPsgBultStuck_flg %d %f\n", VbOUT_PWN_RLPsgBultStuck_flg, Timestamp);
          checkStruct.VbOUT_PWN_RLPsgBultStuck_flg_old = VbOUT_PWN_RLPsgBultStuck_flg;
      }
      if (VbOUT_PWN_ZCULFLWinInitializedSts_flg != checkStruct.VbOUT_PWN_ZCULFLWinInitializedSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ZCULFLWinInitializedSts_flg %d %f\n", VbOUT_PWN_ZCULFLWinInitializedSts_flg, Timestamp);
          checkStruct.VbOUT_PWN_ZCULFLWinInitializedSts_flg_old = VbOUT_PWN_ZCULFLWinInitializedSts_flg;
      }
      if (VbOUT_PWN_ZCULRLWinInitializedSts_flg != checkStruct.VbOUT_PWN_ZCULRLWinInitializedSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ZCULRLWinInitializedSts_flg %d %f\n", VbOUT_PWN_ZCULRLWinInitializedSts_flg, Timestamp);
          checkStruct.VbOUT_PWN_ZCULRLWinInitializedSts_flg_old = VbOUT_PWN_ZCULRLWinInitializedSts_flg;
      }
      if (VbOUT_PWN_DisablePassengerWndStsToEE_flg != checkStruct.VbOUT_PWN_DisablePassengerWndStsToEE_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_DisablePassengerWndStsToEE_flg %d %f\n", VbOUT_PWN_DisablePassengerWndStsToEE_flg, Timestamp);
          checkStruct.VbOUT_PWN_DisablePassengerWndStsToEE_flg_old = VbOUT_PWN_DisablePassengerWndStsToEE_flg;
      }
      if (VeOUT_PWN_PWNStatusResponse2ICM_sig != checkStruct.VeOUT_PWN_PWNStatusResponse2ICM_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_PWNStatusResponse2ICM_sig %d %f\n", VeOUT_PWN_PWNStatusResponse2ICM_sig, Timestamp);
          checkStruct.VeOUT_PWN_PWNStatusResponse2ICM_sig_old = VeOUT_PWN_PWNStatusResponse2ICM_sig;
      }
      if (VeOUT_PWN_PWNStatusResponse2TCP_sig != checkStruct.VeOUT_PWN_PWNStatusResponse2TCP_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_PWNStatusResponse2TCP_sig %d %f\n", VeOUT_PWN_PWNStatusResponse2TCP_sig, Timestamp);
          checkStruct.VeOUT_PWN_PWNStatusResponse2TCP_sig_old = VeOUT_PWN_PWNStatusResponse2TCP_sig;
      }
      if (VeOUT_PWN_PWNStatusResponse2DKM_sig != checkStruct.VeOUT_PWN_PWNStatusResponse2DKM_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_PWNStatusResponse2DKM_sig %d %f\n", VeOUT_PWN_PWNStatusResponse2DKM_sig, Timestamp);
          checkStruct.VeOUT_PWN_PWNStatusResponse2DKM_sig_old = VeOUT_PWN_PWNStatusResponse2DKM_sig;
      }
      if (VbOUT_PWN_ZCULRainAutoClosedWndSts_flg != checkStruct.VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ZCULRainAutoClosedWndSts_flg %d %f\n", VbOUT_PWN_ZCULRainAutoClosedWndSts_flg, Timestamp);
          checkStruct.VbOUT_PWN_ZCULRainAutoClosedWndSts_flg_old = VbOUT_PWN_ZCULRainAutoClosedWndSts_flg;
      }
      if (VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg != checkStruct.VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg %d %f\n", VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg, Timestamp);
          checkStruct.VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg_old = VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg;
      }
      if (VeOUT_PWN_ZCULFRWindowsControl_sig != checkStruct.VeOUT_PWN_ZCULFRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULFRWindowsControl_sig %d %f\n", VeOUT_PWN_ZCULFRWindowsControl_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULFRWindowsControl_sig_old = VeOUT_PWN_ZCULFRWindowsControl_sig;
      }
      if (VeOUT_PWN_ZCULRRWindowsControl_sig != checkStruct.VeOUT_PWN_ZCULRRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULRRWindowsControl_sig %d %f\n", VeOUT_PWN_ZCULRRWindowsControl_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULRRWindowsControl_sig_old = VeOUT_PWN_ZCULRRWindowsControl_sig;
      }
      if (VeOUT_PWN_ZCULFRWINCtrlCmd_sig != checkStruct.VeOUT_PWN_ZCULFRWINCtrlCmd_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULFRWINCtrlCmd_sig %d %f\n", VeOUT_PWN_ZCULFRWINCtrlCmd_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULFRWINCtrlCmd_sig_old = VeOUT_PWN_ZCULFRWINCtrlCmd_sig;
      }
      if (VeOUT_PWN_ZCULRRWINCtrlCmd_sig != checkStruct.VeOUT_PWN_ZCULRRWINCtrlCmd_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULRRWINCtrlCmd_sig %d %f\n", VeOUT_PWN_ZCULRRWINCtrlCmd_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULRRWINCtrlCmd_sig_old = VeOUT_PWN_ZCULRRWINCtrlCmd_sig;
      }
      if (VbOUT_PWN_ALMWinUpError_flg != checkStruct.VbOUT_PWN_ALMWinUpError_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ALMWinUpError_flg %d %f\n", VbOUT_PWN_ALMWinUpError_flg, Timestamp);
          checkStruct.VbOUT_PWN_ALMWinUpError_flg_old = VbOUT_PWN_ALMWinUpError_flg;
      }
      if (VbOUT_PWN_AllWinCloseStsFb_flg != checkStruct.VbOUT_PWN_AllWinCloseStsFb_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_AllWinCloseStsFb_flg %d %f\n", VbOUT_PWN_AllWinCloseStsFb_flg, Timestamp);
          checkStruct.VbOUT_PWN_AllWinCloseStsFb_flg_old = VbOUT_PWN_AllWinCloseStsFb_flg;
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[0] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [0] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_0 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[0], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[0] = VeOUT_PWN_FLWinSrcHst_Array[0];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[1] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [1] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_1 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[1], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[1] = VeOUT_PWN_FLWinSrcHst_Array[1];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[2] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [2] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_2 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[2], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[2] = VeOUT_PWN_FLWinSrcHst_Array[2];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[3] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [3] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_3 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[3], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[3] = VeOUT_PWN_FLWinSrcHst_Array[3];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[4] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [4] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_4 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[4], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[4] = VeOUT_PWN_FLWinSrcHst_Array[4];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[5] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [5] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_5 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[5], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[5] = VeOUT_PWN_FLWinSrcHst_Array[5];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[6] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [6] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_6 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[6], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[6] = VeOUT_PWN_FLWinSrcHst_Array[6];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[7] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [7] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_7 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[7], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[7] = VeOUT_PWN_FLWinSrcHst_Array[7];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[8] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [8] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_8 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[8], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[8] = VeOUT_PWN_FLWinSrcHst_Array[8];
      }
      if (VeOUT_PWN_FLWinSrcHst_Array[9] != checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old [9] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_FLWinSrcHst_Array_9 %d %f\n", VeOUT_PWN_FLWinSrcHst_Array[9], Timestamp);
          checkStruct.VeOUT_PWN_FLWinSrcHst_Array_old[9] = VeOUT_PWN_FLWinSrcHst_Array[9];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[0] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [0] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_0 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[0], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[0] = VeOUT_PWN_RLWinSrcHst_Array[0];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[1] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [1] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_1 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[1], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[1] = VeOUT_PWN_RLWinSrcHst_Array[1];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[2] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [2] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_2 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[2], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[2] = VeOUT_PWN_RLWinSrcHst_Array[2];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[3] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [3] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_3 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[3], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[3] = VeOUT_PWN_RLWinSrcHst_Array[3];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[4] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [4] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_4 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[4], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[4] = VeOUT_PWN_RLWinSrcHst_Array[4];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[5] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [5] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_5 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[5], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[5] = VeOUT_PWN_RLWinSrcHst_Array[5];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[6] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [6] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_6 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[6], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[6] = VeOUT_PWN_RLWinSrcHst_Array[6];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[7] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [7] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_7 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[7], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[7] = VeOUT_PWN_RLWinSrcHst_Array[7];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[8] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [8] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_8 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[8], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[8] = VeOUT_PWN_RLWinSrcHst_Array[8];
      }
      if (VeOUT_PWN_RLWinSrcHst_Array[9] != checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old [9] || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_RLWinSrcHst_Array_9 %d %f\n", VeOUT_PWN_RLWinSrcHst_Array[9], Timestamp);
          checkStruct.VeOUT_PWN_RLWinSrcHst_Array_old[9] = VeOUT_PWN_RLWinSrcHst_Array[9];
      }
      if (VbOUT_PWN_FLWinOhpmode_flg != checkStruct.VbOUT_PWN_FLWinOhpmode_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_FLWinOhpmode_flg %d %f\n", VbOUT_PWN_FLWinOhpmode_flg, Timestamp);
          checkStruct.VbOUT_PWN_FLWinOhpmode_flg_old = VbOUT_PWN_FLWinOhpmode_flg;
      }
      if (VbOUT_PWN_RLWinOhpmode_flg != checkStruct.VbOUT_PWN_RLWinOhpmode_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_RLWinOhpmode_flg %d %f\n", VbOUT_PWN_RLWinOhpmode_flg, Timestamp);
          checkStruct.VbOUT_PWN_RLWinOhpmode_flg_old = VbOUT_PWN_RLWinOhpmode_flg;
      }
      if (VbOUT_PWN_FLForceWinCloseReq_flg != checkStruct.VbOUT_PWN_FLForceWinCloseReq_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_FLForceWinCloseReq_flg %d %f\n", VbOUT_PWN_FLForceWinCloseReq_flg, Timestamp);
          checkStruct.VbOUT_PWN_FLForceWinCloseReq_flg_old = VbOUT_PWN_FLForceWinCloseReq_flg;
      }
      if (VbOUT_PWN_RLForceWinCloseReq_flg != checkStruct.VbOUT_PWN_RLForceWinCloseReq_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_RLForceWinCloseReq_flg %d %f\n", VbOUT_PWN_RLForceWinCloseReq_flg, Timestamp);
          checkStruct.VbOUT_PWN_RLForceWinCloseReq_flg_old = VbOUT_PWN_RLForceWinCloseReq_flg;
      }
      if (VeOUT_PWN_ZCULArmedCloseWndSts_sig != checkStruct.VeOUT_PWN_ZCULArmedCloseWndSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULArmedCloseWndSts_sig %d %f\n", VeOUT_PWN_ZCULArmedCloseWndSts_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULArmedCloseWndSts_sig_old = VeOUT_PWN_ZCULArmedCloseWndSts_sig;
      }
      if (VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig != checkStruct.VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig %d %f\n", VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig_old = VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig;
      }
      if (VbOUT_PWN_ZCULWindowSts_flg != checkStruct.VbOUT_PWN_ZCULWindowSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_ZCULWindowSts_flg %d %f\n", VbOUT_PWN_ZCULWindowSts_flg, Timestamp);
          checkStruct.VbOUT_PWN_ZCULWindowSts_flg_old = VbOUT_PWN_ZCULWindowSts_flg;
      }
      if (VeOUT_PWN_ZCULSourceCMDFR_sig != checkStruct.VeOUT_PWN_ZCULSourceCMDFR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULSourceCMDFR_sig %d %f\n", VeOUT_PWN_ZCULSourceCMDFR_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULSourceCMDFR_sig_old = VeOUT_PWN_ZCULSourceCMDFR_sig;
      }
      if (VeOUT_PWN_ZCULSourceCMDRR_sig != checkStruct.VeOUT_PWN_ZCULSourceCMDRR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULSourceCMDRR_sig %d %f\n", VeOUT_PWN_ZCULSourceCMDRR_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULSourceCMDRR_sig_old = VeOUT_PWN_ZCULSourceCMDRR_sig;
      }
      if (VnOUT_PWN_PWNFailReason2ICM_sig != checkStruct.VnOUT_PWN_PWNFailReason2ICM_sig_old || printfCount == 0) {
          fprintf(fp_output, " VnOUT_PWN_PWNFailReason2ICM_sig %d %f\n", VnOUT_PWN_PWNFailReason2ICM_sig, Timestamp);
          checkStruct.VnOUT_PWN_PWNFailReason2ICM_sig_old = VnOUT_PWN_PWNFailReason2ICM_sig;
      }
      if (VnOUT_PWN_PWNFailReason2TCP_sig != checkStruct.VnOUT_PWN_PWNFailReason2TCP_sig_old || printfCount == 0) {
          fprintf(fp_output, " VnOUT_PWN_PWNFailReason2TCP_sig %d %f\n", VnOUT_PWN_PWNFailReason2TCP_sig, Timestamp);
          checkStruct.VnOUT_PWN_PWNFailReason2TCP_sig_old = VnOUT_PWN_PWNFailReason2TCP_sig;
      }
      if (VnOUT_PWN_PWNFailReason2DKM_sig != checkStruct.VnOUT_PWN_PWNFailReason2DKM_sig_old || printfCount == 0) {
          fprintf(fp_output, " VnOUT_PWN_PWNFailReason2DKM_sig %d %f\n", VnOUT_PWN_PWNFailReason2DKM_sig, Timestamp);
          checkStruct.VnOUT_PWN_PWNFailReason2DKM_sig_old = VnOUT_PWN_PWNFailReason2DKM_sig;
      }
      if (VeOUT_PWN_ZCULSourcebackFL_sig != checkStruct.VeOUT_PWN_ZCULSourcebackFL_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULSourcebackFL_sig %d %f\n", VeOUT_PWN_ZCULSourcebackFL_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULSourcebackFL_sig_old = VeOUT_PWN_ZCULSourcebackFL_sig;
      }
      if (VeOUT_PWN_ZCULSourcebackRL_sig != checkStruct.VeOUT_PWN_ZCULSourcebackRL_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PWN_ZCULSourcebackRL_sig %d %f\n", VeOUT_PWN_ZCULSourcebackRL_sig, Timestamp);
          checkStruct.VeOUT_PWN_ZCULSourcebackRL_sig_old = VeOUT_PWN_ZCULSourcebackRL_sig;
      }
      if (VbOUT_PWN_SleepPermit_flg != checkStruct.VbOUT_PWN_SleepPermit_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PWN_SleepPermit_flg %d %f\n", VbOUT_PWN_SleepPermit_flg, Timestamp);
          checkStruct.VbOUT_PWN_SleepPermit_flg_old = VbOUT_PWN_SleepPermit_flg;
      }
      if (VeINP_LIN_DSPFLWndSwSts_sig != checkStruct.VeINP_LIN_DSPFLWndSwSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_LIN_DSPFLWndSwSts_sig %d %f\n", VeINP_LIN_DSPFLWndSwSts_sig, Timestamp);
          checkStruct.VeINP_LIN_DSPFLWndSwSts_sig_old = VeINP_LIN_DSPFLWndSwSts_sig;
      }
      if (VeINP_LIN_DSPFRWndSwSts_sig != checkStruct.VeINP_LIN_DSPFRWndSwSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_LIN_DSPFRWndSwSts_sig %d %f\n", VeINP_LIN_DSPFRWndSwSts_sig, Timestamp);
          checkStruct.VeINP_LIN_DSPFRWndSwSts_sig_old = VeINP_LIN_DSPFRWndSwSts_sig;
      }
      if (VeINP_LIN_DSPRLWndSwSts_sig != checkStruct.VeINP_LIN_DSPRLWndSwSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_LIN_DSPRLWndSwSts_sig %d %f\n", VeINP_LIN_DSPRLWndSwSts_sig, Timestamp);
          checkStruct.VeINP_LIN_DSPRLWndSwSts_sig_old = VeINP_LIN_DSPRLWndSwSts_sig;
      }
      if (VeINP_LIN_DSPRRWndSwSts_sig != checkStruct.VeINP_LIN_DSPRRWndSwSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_LIN_DSPRRWndSwSts_sig %d %f\n", VeINP_LIN_DSPRRWndSwSts_sig, Timestamp);
          checkStruct.VeINP_LIN_DSPRRWndSwSts_sig_old = VeINP_LIN_DSPRRWndSwSts_sig;
      }
      if (VbINP_HWA_RLPsngManUp_flg != checkStruct.VbINP_HWA_RLPsngManUp_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_RLPsngManUp_flg %d %f\n", VbINP_HWA_RLPsngManUp_flg, Timestamp);
          checkStruct.VbINP_HWA_RLPsngManUp_flg_old = VbINP_HWA_RLPsngManUp_flg;
      }
      if (VbINP_HWA_RLPsngManDn_flg != checkStruct.VbINP_HWA_RLPsngManDn_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_RLPsngManDn_flg %d %f\n", VbINP_HWA_RLPsngManDn_flg, Timestamp);
          checkStruct.VbINP_HWA_RLPsngManDn_flg_old = VbINP_HWA_RLPsngManDn_flg;
      }
      if (VbINP_HWA_RLPsngAutoUp_flg != checkStruct.VbINP_HWA_RLPsngAutoUp_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_RLPsngAutoUp_flg %d %f\n", VbINP_HWA_RLPsngAutoUp_flg, Timestamp);
          checkStruct.VbINP_HWA_RLPsngAutoUp_flg_old = VbINP_HWA_RLPsngAutoUp_flg;
      }
      if (VbINP_HWA_RLPsngAutoDn_flg != checkStruct.VbINP_HWA_RLPsngAutoDn_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_RLPsngAutoDn_flg %d %f\n", VbINP_HWA_RLPsngAutoDn_flg, Timestamp);
          checkStruct.VbINP_HWA_RLPsngAutoDn_flg_old = VbINP_HWA_RLPsngAutoDn_flg;
      }
      if (VbOUT_SP_ZCULFLDoorSts_flg != checkStruct.VbOUT_SP_ZCULFLDoorSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_SP_ZCULFLDoorSts_flg %d %f\n", VbOUT_SP_ZCULFLDoorSts_flg, Timestamp);
          checkStruct.VbOUT_SP_ZCULFLDoorSts_flg_old = VbOUT_SP_ZCULFLDoorSts_flg;
      }
      if (VbOUT_SP_ZCULRLDoorSts_flg != checkStruct.VbOUT_SP_ZCULRLDoorSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_SP_ZCULRLDoorSts_flg %d %f\n", VbOUT_SP_ZCULRLDoorSts_flg, Timestamp);
          checkStruct.VbOUT_SP_ZCULRLDoorSts_flg_old = VbOUT_SP_ZCULRLDoorSts_flg;
      }
      if (VeINP_HWA_Voltage_100mV != checkStruct.VeINP_HWA_Voltage_100mV_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_Voltage_100mV %d %f\n", VeINP_HWA_Voltage_100mV, Timestamp);
          checkStruct.VeINP_HWA_Voltage_100mV_old = VeINP_HWA_Voltage_100mV;
      }
      if (VeINP_HWA_FLFeedBackRunSts_sig != checkStruct.VeINP_HWA_FLFeedBackRunSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_FLFeedBackRunSts_sig %d %f\n", VeINP_HWA_FLFeedBackRunSts_sig, Timestamp);
          checkStruct.VeINP_HWA_FLFeedBackRunSts_sig_old = VeINP_HWA_FLFeedBackRunSts_sig;
      }
      if (VeINP_HWA_RLFeedBackRunSts_sig != checkStruct.VeINP_HWA_RLFeedBackRunSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_RLFeedBackRunSts_sig %d %f\n", VeINP_HWA_RLFeedBackRunSts_sig, Timestamp);
          checkStruct.VeINP_HWA_RLFeedBackRunSts_sig_old = VeINP_HWA_RLFeedBackRunSts_sig;
      }
      if (VeINP_HWA_FLStallSts_sig != checkStruct.VeINP_HWA_FLStallSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_FLStallSts_sig %d %f\n", VeINP_HWA_FLStallSts_sig, Timestamp);
          checkStruct.VeINP_HWA_FLStallSts_sig_old = VeINP_HWA_FLStallSts_sig;
      }
      if (VeINP_HWA_RLStallSts_sig != checkStruct.VeINP_HWA_RLStallSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_RLStallSts_sig %d %f\n", VeINP_HWA_RLStallSts_sig, Timestamp);
          checkStruct.VeINP_HWA_RLStallSts_sig_old = VeINP_HWA_RLStallSts_sig;
      }
      if (VuINP_HWA_FLWinPostion_sig != checkStruct.VuINP_HWA_FLWinPostion_sig_old || printfCount == 0) {
          fprintf(fp_output, " VuINP_HWA_FLWinPostion_sig %d %f\n", VuINP_HWA_FLWinPostion_sig, Timestamp);
          checkStruct.VuINP_HWA_FLWinPostion_sig_old = VuINP_HWA_FLWinPostion_sig;
      }
      if (VuINP_HWA_RLWinPostion_sig != checkStruct.VuINP_HWA_RLWinPostion_sig_old || printfCount == 0) {
          fprintf(fp_output, " VuINP_HWA_RLWinPostion_sig %d %f\n", VuINP_HWA_RLWinPostion_sig, Timestamp);
          checkStruct.VuINP_HWA_RLWinPostion_sig_old = VuINP_HWA_RLWinPostion_sig;
      }
      if (VbINP_HWA_FLAntipinchEnable_flg != checkStruct.VbINP_HWA_FLAntipinchEnable_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_FLAntipinchEnable_flg %d %f\n", VbINP_HWA_FLAntipinchEnable_flg, Timestamp);
          checkStruct.VbINP_HWA_FLAntipinchEnable_flg_old = VbINP_HWA_FLAntipinchEnable_flg;
      }
      if (VbINP_HWA_RLAntipinchEnable_flg != checkStruct.VbINP_HWA_RLAntipinchEnable_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_RLAntipinchEnable_flg %d %f\n", VbINP_HWA_RLAntipinchEnable_flg, Timestamp);
          checkStruct.VbINP_HWA_RLAntipinchEnable_flg_old = VbINP_HWA_RLAntipinchEnable_flg;
      }
      if (VuINP_HWA_FLWinPosMax_sig != checkStruct.VuINP_HWA_FLWinPosMax_sig_old || printfCount == 0) {
          fprintf(fp_output, " VuINP_HWA_FLWinPosMax_sig %d %f\n", VuINP_HWA_FLWinPosMax_sig, Timestamp);
          checkStruct.VuINP_HWA_FLWinPosMax_sig_old = VuINP_HWA_FLWinPosMax_sig;
      }
      if (VuINP_HWA_RLWinPosMax_sig != checkStruct.VuINP_HWA_RLWinPosMax_sig_old || printfCount == 0) {
          fprintf(fp_output, " VuINP_HWA_RLWinPosMax_sig %d %f\n", VuINP_HWA_RLWinPosMax_sig, Timestamp);
          checkStruct.VuINP_HWA_RLWinPosMax_sig_old = VuINP_HWA_RLWinPosMax_sig;
      }
      if (VbINP_CAN_ICMDisablePassengerWndReq_flg != checkStruct.VbINP_CAN_ICMDisablePassengerWndReq_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_ICMDisablePassengerWndReq_flg %d %f\n", VbINP_CAN_ICMDisablePassengerWndReq_flg, Timestamp);
          checkStruct.VbINP_CAN_ICMDisablePassengerWndReq_flg_old = VbINP_CAN_ICMDisablePassengerWndReq_flg;
      }
      if (VbINP_EPRM_DisablePassengerWndStsFromEE_flg != checkStruct.VbINP_EPRM_DisablePassengerWndStsFromEE_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_EPRM_DisablePassengerWndStsFromEE_flg %d %f\n", VbINP_EPRM_DisablePassengerWndStsFromEE_flg, Timestamp);
          checkStruct.VbINP_EPRM_DisablePassengerWndStsFromEE_flg_old = VbINP_EPRM_DisablePassengerWndStsFromEE_flg;
      }
      if (VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg != checkStruct.VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg %d %f\n", VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg, Timestamp);
          checkStruct.VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg_old = VbINP_EPRM_ICMRainAutoClosedWndStsFromEE_flg;
      }
      if (VbINP_CAN_VCCMFRDoorSts_flg != checkStruct.VbINP_CAN_VCCMFRDoorSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMFRDoorSts_flg %d %f\n", VbINP_CAN_VCCMFRDoorSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMFRDoorSts_flg_old = VbINP_CAN_VCCMFRDoorSts_flg;
      }
      if (VbINP_CAN_VCCMRRDoorSts_flg != checkStruct.VbINP_CAN_VCCMRRDoorSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMRRDoorSts_flg %d %f\n", VbINP_CAN_VCCMRRDoorSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMRRDoorSts_flg_old = VbINP_CAN_VCCMRRDoorSts_flg;
      }
      if (VbINP_CAN_VCCMTrunkDoorSts_flg != checkStruct.VbINP_CAN_VCCMTrunkDoorSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMTrunkDoorSts_flg %d %f\n", VbINP_CAN_VCCMTrunkDoorSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMTrunkDoorSts_flg_old = VbINP_CAN_VCCMTrunkDoorSts_flg;
      }
      if (VeINP_CAN_ICMFLWindowsVoiControl_sig != checkStruct.VeINP_CAN_ICMFLWindowsVoiControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMFLWindowsVoiControl_sig %d %f\n", VeINP_CAN_ICMFLWindowsVoiControl_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMFLWindowsVoiControl_sig_old = VeINP_CAN_ICMFLWindowsVoiControl_sig;
      }
      if (VeINP_CAN_ICMRLWindowsVoiControl_sig != checkStruct.VeINP_CAN_ICMRLWindowsVoiControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMRLWindowsVoiControl_sig %d %f\n", VeINP_CAN_ICMRLWindowsVoiControl_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMRLWindowsVoiControl_sig_old = VeINP_CAN_ICMRLWindowsVoiControl_sig;
      }
      if (VeINP_CAN_ICMFRWindowsVoiControl_sig != checkStruct.VeINP_CAN_ICMFRWindowsVoiControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMFRWindowsVoiControl_sig %d %f\n", VeINP_CAN_ICMFRWindowsVoiControl_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMFRWindowsVoiControl_sig_old = VeINP_CAN_ICMFRWindowsVoiControl_sig;
      }
      if (VeINP_CAN_ICMRRWindowsVoiControl_sig != checkStruct.VeINP_CAN_ICMRRWindowsVoiControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMRRWindowsVoiControl_sig %d %f\n", VeINP_CAN_ICMRRWindowsVoiControl_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMRRWindowsVoiControl_sig_old = VeINP_CAN_ICMRRWindowsVoiControl_sig;
      }
      if (VeINP_CAN_TCPFLWindowsControl_sig != checkStruct.VeINP_CAN_TCPFLWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_TCPFLWindowsControl_sig %d %f\n", VeINP_CAN_TCPFLWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_TCPFLWindowsControl_sig_old = VeINP_CAN_TCPFLWindowsControl_sig;
      }
      if (VeINP_CAN_TCPRLWindowsControl_sig != checkStruct.VeINP_CAN_TCPRLWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_TCPRLWindowsControl_sig %d %f\n", VeINP_CAN_TCPRLWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_TCPRLWindowsControl_sig_old = VeINP_CAN_TCPRLWindowsControl_sig;
      }
      if (VeINP_CAN_TCPFRWindowsControl_sig != checkStruct.VeINP_CAN_TCPFRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_TCPFRWindowsControl_sig %d %f\n", VeINP_CAN_TCPFRWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_TCPFRWindowsControl_sig_old = VeINP_CAN_TCPFRWindowsControl_sig;
      }
      if (VeINP_CAN_TCPRRWindowsControl_sig != checkStruct.VeINP_CAN_TCPRRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_TCPRRWindowsControl_sig %d %f\n", VeINP_CAN_TCPRRWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_TCPRRWindowsControl_sig_old = VeINP_CAN_TCPRRWindowsControl_sig;
      }
      if (VeINP_CAN_DKMVCCMFLWindowsControl_sig != checkStruct.VeINP_CAN_DKMVCCMFLWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_DKMVCCMFLWindowsControl_sig %d %f\n", VeINP_CAN_DKMVCCMFLWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_DKMVCCMFLWindowsControl_sig_old = VeINP_CAN_DKMVCCMFLWindowsControl_sig;
      }
      if (VeINP_CAN_DKMVCCMRLWindowsControl_sig != checkStruct.VeINP_CAN_DKMVCCMRLWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_DKMVCCMRLWindowsControl_sig %d %f\n", VeINP_CAN_DKMVCCMRLWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_DKMVCCMRLWindowsControl_sig_old = VeINP_CAN_DKMVCCMRLWindowsControl_sig;
      }
      if (VeINP_CAN_DKMVCCMFRWindowsControl_sig != checkStruct.VeINP_CAN_DKMVCCMFRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_DKMVCCMFRWindowsControl_sig %d %f\n", VeINP_CAN_DKMVCCMFRWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_DKMVCCMFRWindowsControl_sig_old = VeINP_CAN_DKMVCCMFRWindowsControl_sig;
      }
      if (VeINP_CAN_DKMVCCMRRWindowsControl_sig != checkStruct.VeINP_CAN_DKMVCCMRRWindowsControl_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_DKMVCCMRRWindowsControl_sig %d %f\n", VeINP_CAN_DKMVCCMRRWindowsControl_sig, Timestamp);
          checkStruct.VeINP_CAN_DKMVCCMRRWindowsControl_sig_old = VeINP_CAN_DKMVCCMRRWindowsControl_sig;
      }
      if (VeINP_CAN_ICMArmedCloseWndReq_sig != checkStruct.VeINP_CAN_ICMArmedCloseWndReq_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMArmedCloseWndReq_sig %d %f\n", VeINP_CAN_ICMArmedCloseWndReq_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMArmedCloseWndReq_sig_old = VeINP_CAN_ICMArmedCloseWndReq_sig;
      }
      if (VeINP_CAN_RLSWinCloseCmd_sig != checkStruct.VeINP_CAN_RLSWinCloseCmd_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_RLSWinCloseCmd_sig %d %f\n", VeINP_CAN_RLSWinCloseCmd_sig, Timestamp);
          checkStruct.VeINP_CAN_RLSWinCloseCmd_sig_old = VeINP_CAN_RLSWinCloseCmd_sig;
      }
      if (VbINP_CAN_ICMRainAutoClosedWndReq_flg != checkStruct.VbINP_CAN_ICMRainAutoClosedWndReq_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_ICMRainAutoClosedWndReq_flg %d %f\n", VbINP_CAN_ICMRainAutoClosedWndReq_flg, Timestamp);
          checkStruct.VbINP_CAN_ICMRainAutoClosedWndReq_flg_old = VbINP_CAN_ICMRainAutoClosedWndReq_flg;
      }
      if (VeOUT_PDU_ZCULSystemPowerSource_sig != checkStruct.VeOUT_PDU_ZCULSystemPowerSource_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_PDU_ZCULSystemPowerSource_sig %d %f\n", VeOUT_PDU_ZCULSystemPowerSource_sig, Timestamp);
          checkStruct.VeOUT_PDU_ZCULSystemPowerSource_sig_old = VeOUT_PDU_ZCULSystemPowerSource_sig;
      }
      if (VbINP_CAN_VCCMFRWinInitializedSts_flg != checkStruct.VbINP_CAN_VCCMFRWinInitializedSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMFRWinInitializedSts_flg %d %f\n", VbINP_CAN_VCCMFRWinInitializedSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMFRWinInitializedSts_flg_old = VbINP_CAN_VCCMFRWinInitializedSts_flg;
      }
      if (VbINP_CAN_VCCMRRWinInitializedSts_flg != checkStruct.VbINP_CAN_VCCMRRWinInitializedSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMRRWinInitializedSts_flg %d %f\n", VbINP_CAN_VCCMRRWinInitializedSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMRRWinInitializedSts_flg_old = VbINP_CAN_VCCMRRWinInitializedSts_flg;
      }
      if (VeINP_CAN_VCCMFRWindowStatus_sig != checkStruct.VeINP_CAN_VCCMFRWindowStatus_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMFRWindowStatus_sig %d %f\n", VeINP_CAN_VCCMFRWindowStatus_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMFRWindowStatus_sig_old = VeINP_CAN_VCCMFRWindowStatus_sig;
      }
      if (VeINP_CAN_VCCMRRWindowStatus_sig != checkStruct.VeINP_CAN_VCCMRRWindowStatus_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMRRWindowStatus_sig %d %f\n", VeINP_CAN_VCCMRRWindowStatus_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMRRWindowStatus_sig_old = VeINP_CAN_VCCMRRWindowStatus_sig;
      }
      if (VeINP_CAN_VCCMFRWindowsMovSt_sig != checkStruct.VeINP_CAN_VCCMFRWindowsMovSt_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMFRWindowsMovSt_sig %d %f\n", VeINP_CAN_VCCMFRWindowsMovSt_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMFRWindowsMovSt_sig_old = VeINP_CAN_VCCMFRWindowsMovSt_sig;
      }
      if (VeINP_CAN_VCCMRRWindowsMovSt_sig != checkStruct.VeINP_CAN_VCCMRRWindowsMovSt_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMRRWindowsMovSt_sig %d %f\n", VeINP_CAN_VCCMRRWindowsMovSt_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMRRWindowsMovSt_sig_old = VeINP_CAN_VCCMRRWindowsMovSt_sig;
      }
      if (VbINP_CAN_VCCMFRAntipinchSts_flg != checkStruct.VbINP_CAN_VCCMFRAntipinchSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMFRAntipinchSts_flg %d %f\n", VbINP_CAN_VCCMFRAntipinchSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMFRAntipinchSts_flg_old = VbINP_CAN_VCCMFRAntipinchSts_flg;
      }
      if (VbINP_CAN_VCCMRRAntipinchSts_flg != checkStruct.VbINP_CAN_VCCMRRAntipinchSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_VCCMRRAntipinchSts_flg %d %f\n", VbINP_CAN_VCCMRRAntipinchSts_flg, Timestamp);
          checkStruct.VbINP_CAN_VCCMRRAntipinchSts_flg_old = VbINP_CAN_VCCMRRAntipinchSts_flg;
      }
      if (VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig != checkStruct.VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig %d %f\n", VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig, Timestamp);
          checkStruct.VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig_old = VeINP_EPRM_ZCULArmedCloseWndStsFromEE_sig;
      }
      if (VeINP_CAN_VCCMSourcebackFR_sig != checkStruct.VeINP_CAN_VCCMSourcebackFR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMSourcebackFR_sig %d %f\n", VeINP_CAN_VCCMSourcebackFR_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMSourcebackFR_sig_old = VeINP_CAN_VCCMSourcebackFR_sig;
      }
      if (VeINP_CAN_VCCMSourcebackRR_sig != checkStruct.VeINP_CAN_VCCMSourcebackRR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMSourcebackRR_sig %d %f\n", VeINP_CAN_VCCMSourcebackRR_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMSourcebackRR_sig_old = VeINP_CAN_VCCMSourcebackRR_sig;
      }
      if (VeINP_CAN_VCCMRunErrCauseFR_sig != checkStruct.VeINP_CAN_VCCMRunErrCauseFR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMRunErrCauseFR_sig %d %f\n", VeINP_CAN_VCCMRunErrCauseFR_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMRunErrCauseFR_sig_old = VeINP_CAN_VCCMRunErrCauseFR_sig;
      }
      if (VeINP_CAN_VCCMRunErrCauseRR_sig != checkStruct.VeINP_CAN_VCCMRunErrCauseRR_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCCMRunErrCauseRR_sig %d %f\n", VeINP_CAN_VCCMRunErrCauseRR_sig, Timestamp);
          checkStruct.VeINP_CAN_VCCMRunErrCauseRR_sig_old = VeINP_CAN_VCCMRunErrCauseRR_sig;
      }
      if (VmOUT_ALM_AlarmState_enum != checkStruct.VmOUT_ALM_AlarmState_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_ALM_AlarmState_enum %d %f\n", VmOUT_ALM_AlarmState_enum, Timestamp);
          checkStruct.VmOUT_ALM_AlarmState_enum_old = VmOUT_ALM_AlarmState_enum;
      }
      if (VmOUT_ALM_PWNReq_enum != checkStruct.VmOUT_ALM_PWNReq_enum_old || printfCount == 0) {
          fprintf(fp_output, " VmOUT_ALM_PWNReq_enum %d %f\n", VmOUT_ALM_PWNReq_enum, Timestamp);
          checkStruct.VmOUT_ALM_PWNReq_enum_old = VmOUT_ALM_PWNReq_enum;
      }
      if (VeINP_CAN_VCU1NActualGear_sig != checkStruct.VeINP_CAN_VCU1NActualGear_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_VCU1NActualGear_sig %d %f\n", VeINP_CAN_VCU1NActualGear_sig, Timestamp);
          checkStruct.VeINP_CAN_VCU1NActualGear_sig_old = VeINP_CAN_VCU1NActualGear_sig;
      }
      if (VeINP_HWA_FLWindowErrSource_sig != checkStruct.VeINP_HWA_FLWindowErrSource_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_FLWindowErrSource_sig %d %f\n", VeINP_HWA_FLWindowErrSource_sig, Timestamp);
          checkStruct.VeINP_HWA_FLWindowErrSource_sig_old = VeINP_HWA_FLWindowErrSource_sig;
      }
      if (VeINP_HWA_RLWindowErrSource_sig != checkStruct.VeINP_HWA_RLWindowErrSource_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_RLWindowErrSource_sig %d %f\n", VeINP_HWA_RLWindowErrSource_sig, Timestamp);
          checkStruct.VeINP_HWA_RLWindowErrSource_sig_old = VeINP_HWA_RLWindowErrSource_sig;
      }
      if (VeINP_CAN_ICMOTASts_sig != checkStruct.VeINP_CAN_ICMOTASts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMOTASts_sig %d %f\n", VeINP_CAN_ICMOTASts_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMOTASts_sig_old = VeINP_CAN_ICMOTASts_sig;
      }
      if (VeOUT_SP_PowerMode_sig != checkStruct.VeOUT_SP_PowerMode_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_SP_PowerMode_sig %d %f\n", VeOUT_SP_PowerMode_sig, Timestamp);
          checkStruct.VeOUT_SP_PowerMode_sig_old = VeOUT_SP_PowerMode_sig;
      }
      if (VeINP_HWA_SleepCommand_sig != checkStruct.VeINP_HWA_SleepCommand_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_HWA_SleepCommand_sig %d %f\n", VeINP_HWA_SleepCommand_sig, Timestamp);
          checkStruct.VeINP_HWA_SleepCommand_sig_old = VeINP_HWA_SleepCommand_sig;
      }
      if (VeOUT_CMS_ZCULCarMode_sig != checkStruct.VeOUT_CMS_ZCULCarMode_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_CMS_ZCULCarMode_sig %d %f\n", VeOUT_CMS_ZCULCarMode_sig, Timestamp);
          checkStruct.VeOUT_CMS_ZCULCarMode_sig_old = VeOUT_CMS_ZCULCarMode_sig;
      }
      if (VbINP_BSW_EEReady_flg != checkStruct.VbINP_BSW_EEReady_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_BSW_EEReady_flg %d %f\n", VbINP_BSW_EEReady_flg, Timestamp);
          checkStruct.VbINP_BSW_EEReady_flg_old = VbINP_BSW_EEReady_flg;
      }
      if (VbINP_HWA_FLPsngManUp_flg != checkStruct.VbINP_HWA_FLPsngManUp_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_FLPsngManUp_flg %d %f\n", VbINP_HWA_FLPsngManUp_flg, Timestamp);
          checkStruct.VbINP_HWA_FLPsngManUp_flg_old = VbINP_HWA_FLPsngManUp_flg;
      }
      if (VbINP_HWA_FLPsngManDn_flg != checkStruct.VbINP_HWA_FLPsngManDn_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_FLPsngManDn_flg %d %f\n", VbINP_HWA_FLPsngManDn_flg, Timestamp);
          checkStruct.VbINP_HWA_FLPsngManDn_flg_old = VbINP_HWA_FLPsngManDn_flg;
      }
      if (VbINP_HWA_FLPsngAutoUp_flg != checkStruct.VbINP_HWA_FLPsngAutoUp_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_FLPsngAutoUp_flg %d %f\n", VbINP_HWA_FLPsngAutoUp_flg, Timestamp);
          checkStruct.VbINP_HWA_FLPsngAutoUp_flg_old = VbINP_HWA_FLPsngAutoUp_flg;
      }
      if (VbINP_HWA_FLPsngAutoDn_flg != checkStruct.VbINP_HWA_FLPsngAutoDn_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_HWA_FLPsngAutoDn_flg %d %f\n", VbINP_HWA_FLPsngAutoDn_flg, Timestamp);
          checkStruct.VbINP_HWA_FLPsngAutoDn_flg_old = VbINP_HWA_FLPsngAutoDn_flg;
      }
      if (VeINP_CAN_ICMWashModeSwSts_sig != checkStruct.VeINP_CAN_ICMWashModeSwSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeINP_CAN_ICMWashModeSwSts_sig %d %f\n", VeINP_CAN_ICMWashModeSwSts_sig, Timestamp);
          checkStruct.VeINP_CAN_ICMWashModeSwSts_sig_old = VeINP_CAN_ICMWashModeSwSts_sig;
      }
      if (VbINP_CAN_ICMCampingModeSwSts_flg != checkStruct.VbINP_CAN_ICMCampingModeSwSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_ICMCampingModeSwSts_flg %d %f\n", VbINP_CAN_ICMCampingModeSwSts_flg, Timestamp);
          checkStruct.VbINP_CAN_ICMCampingModeSwSts_flg_old = VbINP_CAN_ICMCampingModeSwSts_flg;
      }
      if (VbINP_CAN_ICMOffVehPowerKeepSwSts_flg != checkStruct.VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_ICMOffVehPowerKeepSwSts_flg %d %f\n", VbINP_CAN_ICMOffVehPowerKeepSwSts_flg, Timestamp);
          checkStruct.VbINP_CAN_ICMOffVehPowerKeepSwSts_flg_old = VbINP_CAN_ICMOffVehPowerKeepSwSts_flg;
      }
      if (VbINP_CAN_PEPSRKElockSts_flg != checkStruct.VbINP_CAN_PEPSRKElockSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_PEPSRKElockSts_flg %d %f\n", VbINP_CAN_PEPSRKElockSts_flg, Timestamp);
          checkStruct.VbINP_CAN_PEPSRKElockSts_flg_old = VbINP_CAN_PEPSRKElockSts_flg;
      }
      if (VbINP_CAN_PEPSRKEunlockSts_flg != checkStruct.VbINP_CAN_PEPSRKEunlockSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_PEPSRKEunlockSts_flg %d %f\n", VbINP_CAN_PEPSRKEunlockSts_flg, Timestamp);
          checkStruct.VbINP_CAN_PEPSRKEunlockSts_flg_old = VbINP_CAN_PEPSRKEunlockSts_flg;
      }
      if (VbOUT_PDU_PowerModeValid_flg != checkStruct.VbOUT_PDU_PowerModeValid_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_PDU_PowerModeValid_flg %d %f\n", VbOUT_PDU_PowerModeValid_flg, Timestamp);
          checkStruct.VbOUT_PDU_PowerModeValid_flg_old = VbOUT_PDU_PowerModeValid_flg;
      }
      if (VeOUT_ALM_ZCULAntiThelfSts_sig != checkStruct.VeOUT_ALM_ZCULAntiThelfSts_sig_old || printfCount == 0) {
          fprintf(fp_output, " VeOUT_ALM_ZCULAntiThelfSts_sig %d %f\n", VeOUT_ALM_ZCULAntiThelfSts_sig, Timestamp);
          checkStruct.VeOUT_ALM_ZCULAntiThelfSts_sig_old = VeOUT_ALM_ZCULAntiThelfSts_sig;
      }
      if (VbINP_CAN_ICMInCarCampingSwSts_flg != checkStruct.VbINP_CAN_ICMInCarCampingSwSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_ICMInCarCampingSwSts_flg %d %f\n", VbINP_CAN_ICMInCarCampingSwSts_flg, Timestamp);
          checkStruct.VbINP_CAN_ICMInCarCampingSwSts_flg_old = VbINP_CAN_ICMInCarCampingSwSts_flg;
      }
      if (VbINP_CFG_LeRiRudder_flg != checkStruct.VbINP_CFG_LeRiRudder_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CFG_LeRiRudder_flg %d %f\n", VbINP_CFG_LeRiRudder_flg, Timestamp);
          checkStruct.VbINP_CFG_LeRiRudder_flg_old = VbINP_CFG_LeRiRudder_flg;
      }
      if (VbINP_CAN_DKMWindowClose_flg != checkStruct.VbINP_CAN_DKMWindowClose_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_DKMWindowClose_flg %d %f\n", VbINP_CAN_DKMWindowClose_flg, Timestamp);
          checkStruct.VbINP_CAN_DKMWindowClose_flg_old = VbINP_CAN_DKMWindowClose_flg;
      }
      if (VbINP_CAN_DKMWindowOpen_flg != checkStruct.VbINP_CAN_DKMWindowOpen_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbINP_CAN_DKMWindowOpen_flg %d %f\n", VbINP_CAN_DKMWindowOpen_flg, Timestamp);
          checkStruct.VbINP_CAN_DKMWindowOpen_flg_old = VbINP_CAN_DKMWindowOpen_flg;
      }
      if (VbOUT_CMS_NAPModeSts_flg != checkStruct.VbOUT_CMS_NAPModeSts_flg_old || printfCount == 0) {
          fprintf(fp_output, " VbOUT_CMS_NAPModeSts_flg %d %f\n", VbOUT_CMS_NAPModeSts_flg, Timestamp);
          checkStruct.VbOUT_CMS_NAPModeSts_flg_old = VbOUT_CMS_NAPModeSts_flg;
      }
      setCycle(1);
      printfCount = 1;
  }

}

void setDebugOpen(void) {
	Timestamp = 0;
	fp_output = fopen("Debug.txt", "w");
}

void setDebugClose(void) {
	if (fp_output) {
		fclose(fp_output);
	}
}

void setCycle(UInt8 cycle) {
	Timestamp = Timestamp + cycle/100.0;
}



# ifdef __cplusplus

}
#endif

