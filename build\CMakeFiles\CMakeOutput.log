The system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: F:/MATLAB/mingw64/bin/gcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"

The C compiler identification is GNU, found in "C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/3.20.0-rc3/CompilerIdC/a.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: F:/MATLAB/mingw64/bin/g++.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"

The CXX compiler identification is GNU, found in "C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/3.20.0-rc3/CompilerIdCXX/a.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp

Run Build Command(s):F:/MATLAB/mingw64/bin/mingw32-make.exe -f Makefile cmTC_28546/fast && F:/MATLAB/mingw64/bin/mingw32-make.exe  -f CMakeFiles\cmTC_28546.dir\build.make CMakeFiles/cmTC_28546.dir/build

mingw32-make.exe[1]: Entering directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_28546.dir/CMakeCCompilerABI.c.obj

F:\MATLAB\mingw64\bin\gcc.exe   -v -o CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj -c "F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCCompilerABI.c"

Using built-in specs.

COLLECT_GCC=F:\MATLAB\mingw64\bin\gcc.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: win32

gcc version 8.1.0 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) 

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1.exe -quiet -v -iprefix F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -U_REENTRANT F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=core2 -march=nocona -auxbase-strip CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj -version -o C:\Users\<USER>\AppData\Local\Temp\cc1x3vTs.s

GNU C17 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)

	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP



GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"

ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"

ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/mingw/include"

#include "..." search starts here:

#include <...> search starts here:

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include

End of search list.

GNU C17 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)

	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP



GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072

Compiler executable checksum: bb117049c51d03d971e874cfcb35cac9

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj C:\Users\<USER>\AppData\Local\Temp\cc1x3vTs.s

GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30

COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../libexec/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../lib/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'

Linking C executable cmTC_28546.exe

"F:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_28546.dir\link.txt --verbose=1

"F:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\cmTC_28546.dir/objects.a
F:\MATLAB\mingw64\bin\ar.exe cr CMakeFiles\cmTC_28546.dir/objects.a @CMakeFiles\cmTC_28546.dir\objects1.rsp
F:\MATLAB\mingw64\bin\gcc.exe  -v -Wl,--whole-archive CMakeFiles\cmTC_28546.dir/objects.a -Wl,--no-whole-archive -o cmTC_28546.exe -Wl,--out-implib,libcmTC_28546.dll.a -Wl,--major-image-version,0,--minor-image-version,0 
Using built-in specs.

COLLECT_GCC=F:\MATLAB\mingw64\bin\gcc.exe

COLLECT_LTO_WRAPPER=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: win32

gcc version 8.1.0 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) 

COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../libexec/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../lib/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_28546.exe' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccwt1Z6U.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_28546.exe F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LF:/MATLAB/mingw64/bin/../lib/gcc -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_28546.dir/objects.a --no-whole-archive --out-implib libcmTC_28546.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_28546.exe' '-mtune=core2' '-march=nocona'

mingw32-make.exe[1]: Leaving directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp'




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
  end of search list found
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/include]
  implicit include dirs: [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include;F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed;F:/MATLAB/mingw64/x86_64-w64-mingw32/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):F:/MATLAB/mingw64/bin/mingw32-make.exe -f Makefile cmTC_28546/fast && F:/MATLAB/mingw64/bin/mingw32-make.exe  -f CMakeFiles\cmTC_28546.dir\build.make CMakeFiles/cmTC_28546.dir/build]
  ignore line: [mingw32-make.exe[1]: Entering directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_28546.dir/CMakeCCompilerABI.c.obj]
  ignore line: [F:\MATLAB\mingw64\bin\gcc.exe   -v -o CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj -c "F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCCompilerABI.c"]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=F:\MATLAB\mingw64\bin\gcc.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: win32]
  ignore line: [gcc version 8.1.0 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
  ignore line: [ F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1.exe -quiet -v -iprefix F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -U_REENTRANT F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=core2 -march=nocona -auxbase-strip CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj -version -o C:\Users\<USER>\AppData\Local\Temp\cc1x3vTs.s]
  ignore line: [GNU C17 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
  ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"]
  ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"]
  ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/mingw/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
  ignore line: [End of search list.]
  ignore line: [GNU C17 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
  ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: bb117049c51d03d971e874cfcb35cac9]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj C:\Users\<USER>\AppData\Local\Temp\cc1x3vTs.s]
  ignore line: [GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30]
  ignore line: [COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../libexec/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_28546.dir\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
  ignore line: [Linking C executable cmTC_28546.exe]
  ignore line: ["F:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_28546.dir\link.txt --verbose=1]
  ignore line: ["F:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\cmTC_28546.dir/objects.a]
  ignore line: [F:\MATLAB\mingw64\bin\ar.exe cr CMakeFiles\cmTC_28546.dir/objects.a @CMakeFiles\cmTC_28546.dir\objects1.rsp]
  ignore line: [F:\MATLAB\mingw64\bin\gcc.exe  -v -Wl --whole-archive CMakeFiles\cmTC_28546.dir/objects.a -Wl --no-whole-archive -o cmTC_28546.exe -Wl --out-implib libcmTC_28546.dll.a -Wl --major-image-version 0 --minor-image-version 0 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=F:\MATLAB\mingw64\bin\gcc.exe]
  ignore line: [COLLECT_LTO_WRAPPER=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: win32]
  ignore line: [gcc version 8.1.0 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) ]
  ignore line: [COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../libexec/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_28546.exe' '-mtune=core2' '-march=nocona']
  link line: [ F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccwt1Z6U.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_28546.exe F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LF:/MATLAB/mingw64/bin/../lib/gcc -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_28546.dir/objects.a --no-whole-archive --out-implib libcmTC_28546.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
    arg [F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
    arg [-plugin] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
    arg [-plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
    arg [-plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccwt1Z6U.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
    arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
    arg [-plugin-opt=-pass-through=-luser32] ==> ignore
    arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
    arg [-plugin-opt=-pass-through=-liconv] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [--sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64] ==> ignore
    arg [-m] ==> ignore
    arg [i386pep] ==> ignore
    arg [-Bdynamic] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_28546.exe] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> ignore
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
    arg [--whole-archive] ==> ignore
    arg [CMakeFiles\cmTC_28546.dir/objects.a] ==> ignore
    arg [--no-whole-archive] ==> ignore
    arg [--out-implib] ==> ignore
    arg [libcmTC_28546.dll.a] ==> ignore
    arg [--major-image-version] ==> ignore
    arg [0] ==> ignore
    arg [--minor-image-version] ==> ignore
    arg [0] ==> ignore
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc_eh] ==> lib [gcc_eh]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [-ladvapi32] ==> lib [advapi32]
    arg [-lshell32] ==> lib [shell32]
    arg [-luser32] ==> lib [user32]
    arg [-lkernel32] ==> lib [kernel32]
    arg [-liconv] ==> lib [iconv]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc_eh] ==> lib [gcc_eh]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> ignore
  remove lib [gcc_eh]
  remove lib [msvcrt]
  remove lib [gcc_eh]
  remove lib [msvcrt]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc] ==> [F:/MATLAB/mingw64/lib/gcc]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [F:/MATLAB/mingw64/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [F:/MATLAB/mingw64/lib]
  implicit libs: [mingw32;gcc;moldname;mingwex;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;moldname;mingwex]
  implicit dirs: [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;F:/MATLAB/mingw64/lib/gcc;F:/MATLAB/mingw64/x86_64-w64-mingw32/lib;F:/MATLAB/mingw64/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp

Run Build Command(s):F:/MATLAB/mingw64/bin/mingw32-make.exe -f Makefile cmTC_194c2/fast && F:/MATLAB/mingw64/bin/mingw32-make.exe  -f CMakeFiles\cmTC_194c2.dir\build.make CMakeFiles/cmTC_194c2.dir/build

mingw32-make.exe[1]: Entering directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_194c2.dir/CMakeCXXCompilerABI.cpp.obj

F:\MATLAB\mingw64\bin\g++.exe   -v -o CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj -c "F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCXXCompilerABI.cpp"

Using built-in specs.

COLLECT_GCC=F:\MATLAB\mingw64\bin\g++.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: win32

gcc version 8.1.0 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) 

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1plus.exe -quiet -v -iprefix F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -U_REENTRANT F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=core2 -march=nocona -auxbase-strip CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj -version -o C:\Users\<USER>\AppData\Local\Temp\ccxHMoSg.s

GNU C++14 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)

	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP



GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"

ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"

ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"

ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/mingw/include"

#include "..." search starts here:

#include <...> search starts here:

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include

End of search list.

GNU C++14 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)

	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP



GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072

Compiler executable checksum: 768151575aea5e2fb63ae2dd7f500530

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj C:\Users\<USER>\AppData\Local\Temp\ccxHMoSg.s

GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30

COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../libexec/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../lib/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'

Linking CXX executable cmTC_194c2.exe

"F:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_194c2.dir\link.txt --verbose=1

"F:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\cmTC_194c2.dir/objects.a
F:\MATLAB\mingw64\bin\ar.exe cr CMakeFiles\cmTC_194c2.dir/objects.a @CMakeFiles\cmTC_194c2.dir\objects1.rsp
F:\MATLAB\mingw64\bin\g++.exe  -v -Wl,--whole-archive CMakeFiles\cmTC_194c2.dir/objects.a -Wl,--no-whole-archive -o cmTC_194c2.exe -Wl,--out-implib,libcmTC_194c2.dll.a -Wl,--major-image-version,0,--minor-image-version,0 
Using built-in specs.

COLLECT_GCC=F:\MATLAB\mingw64\bin\g++.exe

COLLECT_LTO_WRAPPER=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: win32

gcc version 8.1.0 (x86_64-win32-seh-rev0, Built by MinGW-W64 project) 

COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../libexec/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;F:/MATLAB/mingw64/bin/../lib/gcc/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_194c2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'

 F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccR4o6wd.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_194c2.exe F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LF:/MATLAB/mingw64/bin/../lib/gcc -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_194c2.dir/objects.a --no-whole-archive --out-implib libcmTC_194c2.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_194c2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'

mingw32-make.exe[1]: Leaving directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp'




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
    add: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
  end of search list found
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
  collapse include dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/include]
  implicit include dirs: [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++;F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32;F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward;F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include;F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed;F:/MATLAB/mingw64/x86_64-w64-mingw32/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):F:/MATLAB/mingw64/bin/mingw32-make.exe -f Makefile cmTC_194c2/fast && F:/MATLAB/mingw64/bin/mingw32-make.exe  -f CMakeFiles\cmTC_194c2.dir\build.make CMakeFiles/cmTC_194c2.dir/build]
  ignore line: [mingw32-make.exe[1]: Entering directory 'C:/Users/<USER>/Desktop/ICAR05_L_OT_ModelTest/build/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_194c2.dir/CMakeCXXCompilerABI.cpp.obj]
  ignore line: [F:\MATLAB\mingw64\bin\g++.exe   -v -o CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj -c "F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCXXCompilerABI.cpp"]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=F:\MATLAB\mingw64\bin\g++.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: win32]
  ignore line: [gcc version 8.1.0 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
  ignore line: [ F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1plus.exe -quiet -v -iprefix F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -U_REENTRANT F:\Program Files\CMake\share\cmake-3.20\Modules\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=core2 -march=nocona -auxbase-strip CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj -version -o C:\Users\<USER>\AppData\Local\Temp\ccxHMoSg.s]
  ignore line: [GNU C++14 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
  ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"]
  ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"]
  ignore line: [ignoring duplicate directory "F:/MATLAB/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"]
  ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/mingw/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
  ignore line: [End of search list.]
  ignore line: [GNU C++14 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
  ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: 768151575aea5e2fb63ae2dd7f500530]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
  ignore line: [ F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj C:\Users\<USER>\AppData\Local\Temp\ccxHMoSg.s]
  ignore line: [GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30]
  ignore line: [COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../libexec/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\cmTC_194c2.dir\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
  ignore line: [Linking CXX executable cmTC_194c2.exe]
  ignore line: ["F:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_194c2.dir\link.txt --verbose=1]
  ignore line: ["F:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\cmTC_194c2.dir/objects.a]
  ignore line: [F:\MATLAB\mingw64\bin\ar.exe cr CMakeFiles\cmTC_194c2.dir/objects.a @CMakeFiles\cmTC_194c2.dir\objects1.rsp]
  ignore line: [F:\MATLAB\mingw64\bin\g++.exe  -v -Wl --whole-archive CMakeFiles\cmTC_194c2.dir/objects.a -Wl --no-whole-archive -o cmTC_194c2.exe -Wl --out-implib libcmTC_194c2.dll.a -Wl --major-image-version 0 --minor-image-version 0 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=F:\MATLAB\mingw64\bin\g++.exe]
  ignore line: [COLLECT_LTO_WRAPPER=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: win32]
  ignore line: [gcc version 8.1.0 (x86_64-win32-seh-rev0  Built by MinGW-W64 project) ]
  ignore line: [COMPILER_PATH=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../libexec/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_194c2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona']
  link line: [ F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccR4o6wd.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_194c2.exe F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LF:/MATLAB/mingw64/bin/../lib/gcc -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_194c2.dir/objects.a --no-whole-archive --out-implib libcmTC_194c2.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
    arg [F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
    arg [-plugin] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
    arg [-plugin-opt=F:/MATLAB/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
    arg [-plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccR4o6wd.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
    arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
    arg [-plugin-opt=-pass-through=-luser32] ==> ignore
    arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
    arg [-plugin-opt=-pass-through=-liconv] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [--sysroot=C:/mingw810/x86_64-810-win32-seh-rt_v6-rev0/mingw64] ==> ignore
    arg [-m] ==> ignore
    arg [i386pep] ==> ignore
    arg [-Bdynamic] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_194c2.exe] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> ignore
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> ignore
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
    arg [-LF:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
    arg [--whole-archive] ==> ignore
    arg [CMakeFiles\cmTC_194c2.dir/objects.a] ==> ignore
    arg [--no-whole-archive] ==> ignore
    arg [--out-implib] ==> ignore
    arg [libcmTC_194c2.dll.a] ==> ignore
    arg [--major-image-version] ==> ignore
    arg [0] ==> ignore
    arg [--minor-image-version] ==> ignore
    arg [0] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [-ladvapi32] ==> lib [advapi32]
    arg [-lshell32] ==> lib [shell32]
    arg [-luser32] ==> lib [user32]
    arg [-lkernel32] ==> lib [kernel32]
    arg [-liconv] ==> lib [iconv]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> ignore
  remove lib [msvcrt]
  remove lib [msvcrt]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc] ==> [F:/MATLAB/mingw64/lib/gcc]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [F:/MATLAB/mingw64/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [F:/MATLAB/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [F:/MATLAB/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [F:/MATLAB/mingw64/lib]
  implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;moldname;mingwex]
  implicit dirs: [F:/MATLAB/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;F:/MATLAB/mingw64/lib/gcc;F:/MATLAB/mingw64/x86_64-w64-mingw32/lib;F:/MATLAB/mingw64/lib]
  implicit fwks: []


