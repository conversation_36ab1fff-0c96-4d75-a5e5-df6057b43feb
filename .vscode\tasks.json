{"version": "2.0.0", "tasks": [{"label": "CMake Configure", "type": "shell", "command": "cmake", "args": ["-B", "${workspaceFolder}/cmake-build-debug", "-S", "${workspaceFolder}"], "group": "build"}, {"label": "CMake Build", "type": "shell", "command": "cmake", "args": ["--build", "${workspaceFolder}/cmake-build-debug", "--config", "Debug"], "group": "build", "dependsOn": ["CMake Configure"]}, {"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "F:\\MATLAB\\mingw64\\bin\\gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}