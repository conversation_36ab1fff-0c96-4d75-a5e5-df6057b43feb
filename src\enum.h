#ifndef ENUM_H
#define <PERSON>NUM_H
typedef enum {
  ALARM_NONE = 0,                       /* Default value */
  ALARM_POWERON,
  ALARM_DISARM1,
  ALARM_DISARM2,
  ALARM_PREARM1,
  ALARM_PREARM2,
  <PERSON><PERSON><PERSON>_ARMING1,
  <PERSON>AR<PERSON>_ARMING2,
  <PERSON>AR<PERSON>_ALARMING,
  ALARM_ARMFAIL,
  ALARM_PENDING,
  ALARM_ALARMING_OFF
} ALARM_STATES_E;

typedef enum {
  REQ_NONE = 0,                       /* Default value */
  LOCK,
  UNLOCK,
  ARMFAIL,
  RELOCK,
  ALARMING_OFF,
  ALARMING_ON,
  ALARMING_UNLOCK,
  RKESEEKCAR1,
  TBOXSEEKCAR1,
  BTSEEKCAR1,
  SEEKCAR_OFF,
  TRUNK_UNLOCK,
  TRUNK_LOCKFAIL,
  TRUNK_LOCK,
  PKE_RELOCK,
  RKE_RELOCK,
  FOLD,
  <PERSON>F<PERSON>D,
  DLK_OH<PERSON>,
  KEYINTRK,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  VOICEALARM,
  F<PERSON>FIREPERMIT,
  <PERSON><PERSON>AR<PERSON>_UNLOCK,
  PRE_LOCK,
  ARMED
} ALM_REQ_E;

typedef enum {
  LOCKSTS_NONE = 0,                       /* Default value */
  ALL_LOCKED,
  ALL_UNLOCKED,
  FL_UNLOCKED,
  LOCK_FAIL,
  UNLOCK_FAIL,
  FL_LOCKED 
} DLK_LOCKSTS_E;

typedef enum {
  LOCK_NONE = 0,                       /* Default value */
  RKE_LOCK,
  RKE_UNLOCK,
  RKE_FLUNLOCK,
  PKE_LOCK,
  PKE_UNLOCK,
  PKE_FLUNLOCK,
  CEN_LOCK,
  CEN_UNLOCK,
  MEC_LOCK,
  MEC_UNLOCK,
  SPEEDLOCK,
  IGNOFF_UNLOCK,
  ALM_RELOCK,
  ALM_RKE_RELOCK,
  ALM_PKE_RELOCK,
  CRASH_UNLOCK,
  DELAYED_UNLOCK,
  TBOX_LOCK,
  TBOX_UNLOCK,
  ANTIFAULT_UNLOCK,
  DRVDROWSNS_LOCK,
  BT_LOCK,
  BT_UNLOCK,
  BT_FLUNLOCK,
  CDU_LOCK,
  CDU_UNLOCK,
  XPU_UNLOCK,
  SCU_UNLOCK,
  VCU_UNLOCK,
  POLLING_LOCK,
  POLLING_UNLOCK,
  IGNON_UNLOCK,
  PENEAR_UNLOCK,
  PENEAR_FLUNLOCK,
  PEAWAY_LOCK,
  DISARM_EVENT,
  ARM_EVENT,
  HEAT_UNLOCK,
  FIS_LOCK,
  FIS_UNLOCK,
  FRIENDMODE_LOCK,
  FRIENDMODE_UNLOCK,
  APA_LOCK,
  APA_UNLOCK,
  ALM_UNLOCK,
  BTRKE_LOCK,
  BTRKE_UNLOCK,
  NFC_LOCK,
  NFC_UNLOCK,
  AVP_LOCK,
  AVP_UNLOCK,
  IHU_LOCK,
  IHU_UNLOCK,
  BT_FLLOCK,
  TBOX_FLLOCK,
  TBOX_FLUNLOCK,
  PEPS_UNLOCK,
  PEPS_LOCK,
  PEPS_WELCOME_UNLOCK,
  PEPS_WELCOME_LOCK,
  WASH_AUTOLOCK,
  CAMPING_AUTOLOCK,
  CRASHAUTOTIME_UNLOCK,
  CRASHAUTO_UNLOCK
} DLK_LOCKTYPE_E;

typedef enum {
  OFF = 0,                       /* Default value */
  Low,
  High,
  Int,
  Mist,
  Auto_Idle,
  Auto_low,
  Auto_high,
  Wiper_Repair,
  Wash_and_wipe,
  Wiper_stall,
  Wiper_Check_Park
} FWIPER_STATUS_E;

typedef enum {
  WIN_UP = 0,                       /* Default value */
  WIN_DN,
  WIN_OFF,
  Invalid
} WIN_ACT_CMD_E;

typedef enum {
  WIN_A_STOP = 0,                       /* Default value */
  WIN_M_CLOSING_CMD,
  WIN_M_OPENING_CMD,
  WIN_A_CLOSING_CMD,
  WIN_A_OPENING_CMD
} WIN_ACT_INPUT_CMD_E;

typedef enum {
  TRKUNLOCK_NONE = 0,                       /* Default value */
  RKE_TRKUNLOCK,
  RKE_PLGUNLOCK,
  PKE_TRKUNLOCK,
  PKE_PLGUNLOCK,
  TBOX_TRKUNLOCK,
  TBOX_PLGUNLOCK,
  BT_TRKUNLOCK,
  BT_PLGUNLOCK,
  OUTDOORSW_TRKUNLOCK,
  OUTDOORSW_PLGUNLOCK,
  PKE_TRKANTIUNLOCK,
  PKE_PLGANTIUNLOCK,
  CDC_TRKUNLOCK,
  CDC_PLGUNLOCK,
  TBOX_PLGLOCK,
  BT_PLGLOCK,
  VOICE_TRKUNLOCK
} TRK_SOURCE_E;

typedef enum {
  TURN_NONE = 0,                       /* Default value */
  TURN_ALARMING,
  TURN_HAZARD_SW,
  TURN_HAZARD_CRASH,
  TURN_LEFT,
  TURN_RIGHT,
  TURN_LEFT_LANE_CHANGE,
  TURN_RIGHT_LANE_CHANGE,
  TURN_HAZARD_BRAKE,
  TURN_RCW_WARNING,
  TURN_ALARM_LOCK,
  TURN_ALARM_UNLOCK,
  TURN_ARMFAIL,
  TURN_TRUNK_UNLOCK,
  TURN_DKM_CARLOCATING,
  TURN_TCP_CARLOCATING,
  TURN_HAZARD_HVBATT,
  TURN_HAZARD_VCUREQ,
  TURN_HAZARD_IDMREQ,
  TURN_RELOCK,
  TURN_BSD_RIGHTLONG,
  TURN_BSD_LEFTLONG,
  TURN_BSD_RIGHTFLASH,
  TURN_BSD_LEFTFLASH,
  TURN_BSD_BOTHLONG,
  TURN_BSD_BOTHFLASH,
  TURN_PEPS_CARLOCATING,
  TURN_HAZARD_LRCR,
  TURN_APA_RIGHT,
  TURN_APA_LEFT,
  TURN_HAZARD_APA,
  TURN_HAZARD_RLR,
  TURN_HAZARD_ICM,
  TURN_HAZARD_RRR,
  TURN_AC_ON,
  TURN_AC_OFF,
} TURN_REQ_E;

typedef enum {
  HORN_NONE = 0,                       /* Default value */
  HORN_SW,
  HORN_LOCK,
  HORN_FALSE_LOCK,
  HORN_UNLOCK,
  HORN_LOCKFAIL,
  HORN_ALMING_ON,
  HORN_SEEKCAR,
  HORN_TCPREMOTE,
  HORN_ALMING_OFF,
  HORN_SENTINEL
} HORN_REQ_E;

#endif
