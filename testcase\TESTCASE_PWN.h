#include "gtest/gtest.h"
#include "TEST_API.h"
#include "RTE.h"
#include <iostream>
#include<fstream>
#include<sstream>
#include<vector>
#include <string>
#include "stdio.h"
#include "bits/stdc++.h"
#include "direct.h"
#include<time.h>
#include<random>
#include<math.h>
#include "PWNL.h"
using namespace std;
#define Up_counting 3    ////���ϼ�����+��
#define Down_counting 2  ////���¼�����+��
#define stop_counting 1  ////��Ч������-��
#define One_way_Max_Time   1000  ////�������˶����ʱ�䣩
#define Thermal_protection_Max  12500  ////���ȱ������ֵ������
#define Maximum_value 1500

static UInt8 *FL_WndSwSts_Switch=(UInt8 *)&VeINP_LIN_DSPFLWndSwSts_sig;////��ʻ����ǰ�Ŵ�������������״̬;
static UInt8 *FL_FeedBack=(UInt8 *)&VeINP_HWA_FLFeedBackRunSts_sig;////��ʻ������������״̬;
static UInt8 *FL_MovT_Status=(UInt8 *)&VmOUT_PWN_FLOutMovTypeState_enum;////��ʻ�೵���˶�״̬;
static UInt16 *FL_Postion=(UInt16 *)&VuINP_HWA_FLWinPostion_sig;////��ʻ�೵��λ���ź�;
static UInt16 *FL_MAXPostion=(UInt16 *)&VuINP_HWA_FLWinPosMax_sig;////��ʻ�೵����������ʶ�𵽵����λ��;
static UInt8 *FL_Anti_pinch =(UInt8 *)&VeINP_HWA_FLWindowErrSource_sig;////��ǰ������ǰ����״̬;

static UInt8 *RL_WndSwSts_Switch=(UInt8 *)&VeINP_LIN_DSPRLWndSwSts_sig;////��ʻ������Ŵ�������������״̬;
static UInt8 *RL_FeedBack=(UInt8 *)&VeINP_HWA_RLFeedBackRunSts_sig;////�������������״̬;
static UInt8 *RL_MovT_Status=(UInt8 *)&VmOUT_PWN_RLOutMovTypeState_enum;////���೵���˶�״̬;
static UInt16 *RL_Postion=(UInt16 *)&VuINP_HWA_RLWinPostion_sig;////��󳵴�λ���ź�;
static UInt16 *RL_MAXPostion=(UInt16 *)&VuINP_HWA_RLWinPosMax_sig;////��󳵴���������ʶ�𵽵����λ��;
static UInt8 *RL_Anti_pinch =(UInt8 *)&VeINP_HWA_RLWindowErrSource_sig;////��󳵴���ǰ����״̬;

static UInt8 *FL_Stall_Status=(UInt8 *)&VeINP_HWA_FLStallSts_sig;////��ʻ������ת״̬;
static UInt8 *RL_Stall_Status=(UInt8 *)&VeINP_HWA_RLStallSts_sig;////�������ת״̬;

/**
 *
 * @param Stall_Status  ��ת״̬
 * @param Stall_Value   ��ת����
 * @param Motor_Status  ���״̬
 * @param Motor_Value   �������
 */
static void Trigger_stall(UInt8 &Stall_Status,int Stall_Value,UInt8 &Motor_Status,int Motor_Value)
{
    Stall_Status=Stall_Value;
    Motor_Status=Motor_Value;
}
////Trigger_stall(*FL_Stall_Status,1,*FL_FeedBack,0);////��ǰ���϶�ת;
////Trigger_stall(*FL_Stall_Status,2,*FL_FeedBack,0);////��ǰ���¶�ת;
////Trigger_stall(*RL_Stall_Status,1,*RL_FeedBack,0);////������϶�ת;
////Trigger_stall(*RL_Stall_Status,2,*RL_FeedBack,0);////������¶�ת;
////Trigger_stall(*FL_Anti_pinch,1,*FL_FeedBack,0);////��ǰ����;
////Trigger_stall(*RL_Anti_pinch,1,*RL_FeedBack,0);////������;

static int Internal_counting=0;
static int Mark_bit=0;
/**
 * @param Trigger_thermal_protection �����ȱ���
 * @param State_of_motion �˶�״̬
 * @param Decided �Ƿ��ӡ��ǰ������1����ӡ 0������ӡ
 */
static int Trigger_thermal_protection(UInt8 &State_of_motion,int Decided)
{
    if(Decided==0)
    {
        if((State_of_motion==3||State_of_motion==1)&&Mark_bit==1)
        {
            Internal_counting=Internal_counting+Up_counting;
        }
        else if((State_of_motion==2||State_of_motion==4)&&Mark_bit==2)
        {
            Internal_counting=Internal_counting+Down_counting;
        }
        else if(State_of_motion==0&&Internal_counting>0&&Mark_bit==0)
        {
            Internal_counting=Internal_counting-stop_counting;
        }

        if((State_of_motion==3||State_of_motion==1)&&Mark_bit==0)
        {
            Mark_bit=1;
        }
        else if((State_of_motion==2||State_of_motion==4)&&Mark_bit==0)
        {
            Mark_bit=2;
        }
        else if(State_of_motion==0&&Internal_counting>0)
        {
            Mark_bit=0;
        }
    }

    return Internal_counting;
}

static void TEST_PWN_SILINIT()
{
    TEST_PWNINIT();
    PWNL_DW.sf_window.u16_heatTemp=0;
    Internal_counting=0;
    Mark_bit=0;

}

static void TestPWN_SILStep(int cycles,UInt8 &Trigger_thermal)
{
    for(int i=0;i<cycles;i++)
    {
        TestPWNStep(1);
        Trigger_thermal_protection(Trigger_thermal,0);////���ƽϴ��������Ҫ�޸�;
    }
}

/**
 * Function Dec Thermal_protection �ȱ���
 * @param HWA_Switch_UP ����Ӳ�߿�������
 * @param UP ����Ӳ�߿���������Чֵ
 * @param HWA_Switch_DOWM ����Ӳ�߿�������
 * @param DOWN ����Ӳ�߿���������Чֵ
 * @param FeedBack   �������״̬
 * @param time   �������״̬�ȴ�ʱ��
 */
static UInt16 Thermal_protection(UInt8 &HWA_Switch_UP,int UP,UInt8 &HWA_Switch_DOWM,int DOWN,UInt8 &FeedBack,int UP1,int UP2,int time,UInt8 &MOV_Signal)
{
    TestPWN_SILStep(10,MOV_Signal);
    for(float i=0;i<6;i++)
    {
        HWA_Switch_UP=UP;
        TestPWN_SILStep(time,MOV_Signal);
        FeedBack=UP1;
        for(int p=0;p<One_way_Max_Time;p++)
        {
            TestPWN_SILStep(1,MOV_Signal);
            if(Trigger_thermal_protection(MOV_Signal,1)>Thermal_protection_Max)
            {
                goto c;
            }
        }
        if(Trigger_thermal_protection(MOV_Signal,1)>Thermal_protection_Max)
        {
            c:;
            break;
        }
        HWA_Switch_DOWM=DOWN;
        TestPWN_SILStep(time,MOV_Signal);
        FeedBack=UP2;
        for(int p=0;p<One_way_Max_Time;p++)
        {
            TestPWN_SILStep(1,MOV_Signal);
            if(Trigger_thermal_protection(MOV_Signal,1)>Thermal_protection_Max)
            {
                goto d;
            }
        }
        if(Trigger_thermal_protection(MOV_Signal,1)>Thermal_protection_Max)
        {
            d:;
            break;
        }
    }
    FeedBack=0;
    return Trigger_thermal_protection(MOV_Signal,1);
}
////UInt16 Final_output=Thermal_protection(*FL_WndSwSts_Switch,1,*FL_WndSwSts_Switch,2,*FL_FeedBack,1,2,10,*FL_MovT_Status);////��ǰ�����ȱ���;
////UInt16 Final_output=Thermal_protection(*RL_WndSwSts_Switch,1,*RL_WndSwSts_Switch,2,*RL_FeedBack,1,2,10,*RL_MovT_Status);////��󴥷��ȱ���;

/**
 * @param Left_window_feedback ����˶�ģ��
 * @param Switch_time ������Чʱ��
 * @param Run_time ģ���˶�ʱ��
 * @param Current_location ��ǰλ��
 * @param Unit_value �˶��ĵ�λֵ
 * @param Motor_signal ����ź�
 * @param feedback_begin �˶�����
 * @param feedback_end �����˶�����
 * @param Max_position ���λ��
 */
static void Left_window_feedback(int Switch_time,int Run_time,UInt16 &Current_location,int Unit_value,UInt8 &Motor_signal,int feedback_begin,int feedback_end,UInt16 Max_position)
{
    TestPWNStep(Switch_time);
    Motor_signal=feedback_begin;
    for(int i=0;i<Run_time;i++)
    {
        TestPWNStep(1);
        if(feedback_begin==2)////OPEN
        {
            Current_location=Current_location+Unit_value;
            if(Current_location>Max_position)
            {
                Current_location=Max_position;
            }
        }
        else if(feedback_begin==1)////CLOSE
        {
            Current_location=Current_location-Unit_value;
            if(Max_position==Maximum_value)
            {
                if(Current_location<0)
                {
                    Current_location=0;
                }
            }
            else
            {
                if(Current_location<Max_position)
                {
                    Current_location=Max_position;
                }
            }
        }
    }
    Motor_signal=feedback_end;
}
////Left_window_feedback(10,*FL_MAXPostion-*FL_Postion,*FL_Postion,1,*FL_FeedBack,1,0,*FL_MAXPostion);////��ǰ--���������λ��;
////Left_window_feedback(10,*FL_MAXPostion-*FL_Postion,*FL_Postion,1,*FL_FeedBack,2,0,*FL_MAXPostion);////��ǰ--�½������λ��;
////Left_window_feedback(10,*RL_MAXPostion-*RL_Postion,*RL_Postion,1,*RL_FeedBack,1,0,*RL_MAXPostion);////���--���������λ��;
////Left_window_feedback(10,*RL_MAXPostion-*RL_Postion,*RL_Postion,1,*RL_FeedBack,2,0,*RL_MAXPostion);////���--�½������λ��;

static int numm()
{
    int x=rand()%(11);
    return x;
}

/**
 * @param Signal_detection ����ź�
 * @param Signal_adress ����ź�
 * @param Time ���ʱ��
 * @param Kepp_OUT �м����ֵ
 * @param End_out ��ʱ����������ֵ
 */
static void Signal_detection(uint8_T &Signal_adress, int Time,int Kepp_OUT,int End_out)
{
    int numarry_Time=0,temp=0;
    for(int i=1;i<Time*2+1;i++)
    {
        TestPWNStep(1);
        if(temp==0)
        {
            if(Signal_adress!=Kepp_OUT)
            {
                if(i>5)
                {
                    numarry_Time=i;
                    break;
                }
                continue;
            }
            else
            {
                temp=1;
            }
        }
        if(temp==1)
        {
            if(Kepp_OUT==End_out)
            {
                if(Signal_adress!=End_out)
                {
                    numarry_Time=i;
                    break;
                }
                else
                {
                    numarry_Time=i;
                }
            }
            if(Kepp_OUT!=End_out)
            {
                if(Signal_adress==End_out||Signal_adress!=Kepp_OUT)
                {
                    numarry_Time=i;
                    break;
                }
            }
        }

    }

    if((numarry_Time>=(Time-2)&&numarry_Time<=(Time+5))||numarry_Time==Time*2)
    {
        EXPECT_EQ(true,true);
    }
    else
    {
        EXPECT_EQ(true,false);
    }
}


static void TEST_TEMP(int Test_1,string Test_2,UInt32 &Test_3,UInt32 time)
{
    int temp=Test_1,num_cycle=0;
    for(int i=1;i<=time;i++)
    {
        TestPWNStep(1);
        if(temp!=Test_3)
        {
            temp=Test_3;
            num_cycle++;
            cout<<"������ʱ��Ϊ��"<<i<<"ticks��"<<Test_2<<"��ֵ�仯Ϊ��"<<temp<<"\n";
        }
    }
    cout<<"ѭ������Ϊ��"<<num_cycle/2<<endl;
}

static string All_out_string[40]={"VeOUT_PWN_ZCULFLWindowStatus_sig","VeOUT_PWN_ZCULRLWindowStatus_sig","VbOUT_PWN_ZCULDisablePassengerWndSts_flg","VmOUT_PWN_FLOutMovTypeState_enum","VmOUT_PWN_RLOutMovTypeState_enum","VmOUT_PWN_FLOutMotorState_enum","VmOUT_PWN_RLOutMotorState_enum","VbOUT_PWN_FLDrvBultStuck_flg","VbOUT_PWN_FRDrvBultStuck_flg","VbOUT_PWN_RLDrvBultStuck_flg","VbOUT_PWN_RRDrvBultStuck_flg","VbOUT_PWN_RLPsgBultStuck_flg","VbOUT_PWN_ZCULFLWinInitializedSts_flg","VbOUT_PWN_ZCULRLWinInitializedSts_flg","VbOUT_PWN_DisablePassengerWndStsToEE_flg","VeOUT_PWN_PWNStatusResponse2ICM_sig","VeOUT_PWN_PWNStatusResponse2TCP_sig","VeOUT_PWN_PWNStatusResponse2DKM_sig","VbOUT_PWN_ZCULRainAutoClosedWndSts_flg","VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg","VeOUT_PWN_ZCULFRWindowsControl_sig","VeOUT_PWN_ZCULRRWindowsControl_sig","VeOUT_PWN_ZCULFRWINCtrlCmd_sig","VeOUT_PWN_ZCULRRWINCtrlCmd_sig","VbOUT_PWN_ALMWinUpError_flg","VbOUT_PWN_AllWinCloseStsFb_flg","VeOUT_PWN_FLWinSrcHst_Array[10]","VeOUT_PWN_RLWinSrcHst_Array[10]","VbOUT_PWN_FLWinOhpmode_flg","VbOUT_PWN_RLWinOhpmode_flg","VbOUT_PWN_FLForceWinCloseReq_flg","VbOUT_PWN_RLForceWinCloseReq_flg","VeOUT_PWN_ZCULArmedCloseWndSts_sig","VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig","VbOUT_PWN_ZCULWindowSts_flg","VeOUT_PWN_ZCULSourceCMDFR_sig","VeOUT_PWN_ZCULSourceCMDRR_sig","VeOUT_PWN_ZCULSourcebackRL_sig","VbOUT_PWN_SleepPermit_flg","VeOUT_PWN_ZCULSourcebackFL_sig"};

static UInt8 *All_Out[40]={&VeOUT_PWN_ZCULFLWindowStatus_sig,&VeOUT_PWN_ZCULRLWindowStatus_sig,&VbOUT_PWN_ZCULDisablePassengerWndSts_flg,&VmOUT_PWN_FLOutMovTypeState_enum,&VmOUT_PWN_RLOutMovTypeState_enum,&VmOUT_PWN_FLOutMotorState_enum,&VmOUT_PWN_RLOutMotorState_enum,&VbOUT_PWN_FLDrvBultStuck_flg,&VbOUT_PWN_FRDrvBultStuck_flg,&VbOUT_PWN_RLDrvBultStuck_flg,&VbOUT_PWN_RRDrvBultStuck_flg,&VbOUT_PWN_RLPsgBultStuck_flg,&VbOUT_PWN_ZCULFLWinInitializedSts_flg,&VbOUT_PWN_ZCULRLWinInitializedSts_flg,&VbOUT_PWN_DisablePassengerWndStsToEE_flg,&VeOUT_PWN_PWNStatusResponse2ICM_sig,&VeOUT_PWN_PWNStatusResponse2TCP_sig,&VeOUT_PWN_PWNStatusResponse2DKM_sig,&VbOUT_PWN_ZCULRainAutoClosedWndSts_flg,&VbOUT_PWN_ICMRainAutoClosedWndStsToEE_flg,&VeOUT_PWN_ZCULFRWindowsControl_sig,&VeOUT_PWN_ZCULRRWindowsControl_sig,&VeOUT_PWN_ZCULFRWINCtrlCmd_sig,&VeOUT_PWN_ZCULRRWINCtrlCmd_sig,&VbOUT_PWN_ALMWinUpError_flg,&VbOUT_PWN_AllWinCloseStsFb_flg,&VeOUT_PWN_FLWinSrcHst_Array[10],&VeOUT_PWN_RLWinSrcHst_Array[10],&VbOUT_PWN_FLWinOhpmode_flg,&VbOUT_PWN_RLWinOhpmode_flg,&VbOUT_PWN_FLForceWinCloseReq_flg,&VbOUT_PWN_RLForceWinCloseReq_flg,&VeOUT_PWN_ZCULArmedCloseWndSts_sig,&VeOUT_PWN_ZCULArmedCloseWndStsToEE_sig,&VbOUT_PWN_ZCULWindowSts_flg,&VeOUT_PWN_ZCULSourceCMDFR_sig,&VeOUT_PWN_ZCULSourceCMDRR_sig,&VeOUT_PWN_ZCULSourcebackRL_sig,&VbOUT_PWN_SleepPermit_flg,&VeOUT_PWN_ZCULSourcebackFL_sig};

////Test_1��ʼֵ  Time���ʱ�� All_signal_address���ֵ��ַ ���ֵ�ź�All_PEPS_string_address
static void TEST_TEMP_Cycle(int32_T Time,UInt8 **All_signal_address,string *All_PEPS_string_address)  ////�����ʱ��7Сʱ
{
    int32_T **Jump_time_struct;
    int32_T **Jump_value_struct;
    int Number_Cycle= sizeof (All_out_string) / sizeof (string);
    int Bool=-1;
    int temp[Number_Cycle];
    Jump_time_struct=(int **)malloc(Number_Cycle * sizeof(int *));
    Jump_value_struct=(int **)malloc(Number_Cycle * sizeof(int *));
    for(int32_T i=0;i<Number_Cycle;i++)
    {
        Jump_time_struct[i]=(int *)malloc(Time * sizeof(int));
        Jump_value_struct[i]=(int *)malloc(Time * sizeof(int));
    }

    for(int32_T i=0;i<Time;i++)
    {
        for(int p=0;p<Number_Cycle;p++)
        {
            *(*(Jump_time_struct+p)+i)=0;
            *(*(Jump_value_struct+p)+i)=0;
            temp[p]=0;
        }
    }

    for(int32_T i=0;i<Time;)
    {
        ////���ӱ���
        for(int p=0;p<Number_Cycle;)
        {
            if(temp[p]!=*All_signal_address[p])
            {
                temp[p]=*All_signal_address[p];
                *(*(Jump_value_struct+p)+i)=*All_signal_address[p];
                if(i==0)
                {
                    *(*(Jump_time_struct+p)+i)=Bool;
                }
                else
                {
                    *(*(Jump_time_struct+p)+i)=i;
                }
            }
            p++;
        }
        TestPWNStep(1);
        i++;
    }

    for(int p=0;p<Number_Cycle;)
    {
        int cycle_suum=0;
        for(int32_T i=0;i<Time;i++)
        {
            if(*(*(Jump_time_struct+p)+i)!=0||*(*(Jump_time_struct+p)+i)==-1)
            {
                cout<<"\033[300;35;1m"<<All_PEPS_string_address[p]<<"\033[1m"<<endl;
                for(;i<Time;)
                {
                    if(*(*(Jump_time_struct+p)+i)!=0)
                    {
                        ++cycle_suum;
                        if(*(*(Jump_time_struct+p)+i)==-1)
                        {
                            cout<<"\033[300;33;1m"<<"�����ʱ�䣺"<<"\033[300;32;1m"<<0<<"\033[1m"<<"     ";
                        }
                        else
                        {
                            cout<<"\033[300;33;1m"<<"�����ʱ�䣺"<<"\033[300;32;1m"<<*(*(Jump_time_struct+p)+i)<<"\033[1m"<<"     ";
                        }
                        cout<<"\033[300;33;1m"<<"�����ֵ��"<<"\033[300;36;1m"<<*(*(Jump_value_struct+p)+i)<<"\033[1m"<<endl;
                    }
                    i++;
                    if((i==Time-1)&&cycle_suum!=0)
                    {
                        if(cycle_suum%2!=0)
                        {
                            cycle_suum=cycle_suum+1;
                        }
                        cout<<"\033[300;34;1m"<<"���ź�����Ϊ��Чֵ������"<<cycle_suum/2<<"\033[1m"<<endl;
                    }
                }
            }
        }
        p++;
    }
    cout<<"----------------------�����������-----------------------------"<<endl;

}


